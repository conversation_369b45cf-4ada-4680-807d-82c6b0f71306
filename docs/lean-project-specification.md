# Lean Project Specification

## Ideation

### The data structure lifted to lean

Each VC should be lifted to a lean theorem.

Let's proceed by example. Look at the following rust program.

```rust
#[pre = "a >= b"]
//    We should also include the safety of operations and lifting a and b to
//    Nat. This would add: 0 <= a> /\ a <= 255 /\ 0 >= b /\ b <= 255
#[post = "ret == a - b"]
//    Safety should also add: 0 <= ret /\ ret <= 255
fn unsafe_subtract(a: u8, b: u8) -> u8 {
    a - b
}

fn main() -> u8 {
    let diff = unsafe_subtract(10, 5);
    diff
}
```

There are two VCs here. One for `unsafe_subtract` and one for `main`.

```lean
-- Lifted from proving the specification of `unsafe_subtract`.
--   Note that the trivial a - b == a - b is going to come from computing the WP
--   of the function body. IT will be discharged.
theorem unsafe_subtract_spec :
  Ctx -> a >= b -> a - b == a - b := sorry

-- Lifted from proving the use of `unsafe_subtract`. Where Ctx is the context
-- at the point of use.
theorem main_line_1_use_of_unsafe_subtract :
  Ctx -> unsafe_subtract.pre(10, 5) := sorry
```

A VC should have:
  - A name.
  - A source location.
  - A proposition.
  - A context.
    - These are absolutely enormous. We need to do some optimization here.

What is in the context?
  - The types of all variables in scope.
  - The values of all constants in scope.
  - All known propositions about variables in scope.
    - Can we trim this to relevant ones?



TODO:
  - Implement primitive type VC generation
    - Arguments of functions must have 