# Appendix: Common Panic Conditions in Safe Rust

## Numbers & Operators

* Integer overflow (debug builds)
  let _ = u8::MAX + 1; // debug: panic; release: wraps
* Integer division/remainder by zero
  10 / 0;  10 % 0; // panic
* Shift amount out of range (≥ bit width or negative via cast)
  1u8 << 8; // panic

## Indexing, Slicing, & Strings

* Out-of-bounds indexing (arrays, slices, Vec, String, etc.)
  let v = vec![1,2,3]; let _ = v[5]; // panic
* Invalid slice ranges (start > end or bounds outside length)
  &v[4..2]; // panic
* str/String slicing not on char boundaries
  let s = "é"; &s[0..1]; // panic

## Option/Result & Assertions

* unwrap / expect on error values
  None::<i32>.unwrap(); // panic
  Err("e").expect("msg"); // panic
* Assertions & sentinels
  assert!(false); assert_eq!(1,2); unreachable!(); todo!(); unimplemented!(); // panic

## Borrow-Checking at Runtime

* RefCell borrow rule violations (dynamic checks)
  let c = std::cell::RefCell::new(0);
  let _b1 = c.borrow_mut();
  let _b2 = c.borrow_mut(); // panic: already mutably borrowed

## Collections & Slices: API Contracts

* Invalid indices for operations like insert, remove, split_at, swap, split_off, drain
  v.insert(10, 1); // panic if 10 > len
* Mismatched lengths in slice ops
  dst.copy_from_slice(src); // panic if len differs
* Capacity math overflow
  Vec::reserve(n) / with_capacity(n) may panic on capacity overflow (too large to represent).
  (Note: true OOM typically aborts the process, not panic.)

## Iterators & Ranges

* Iterator::step_by(0)
  (0..10).step_by(0); // panic
* nth_back / split_at-style misuse in some adapters when ranges are invalid
  (Follows same principles as slicing: invalid positions => panic.)

## Time

* Instant::duration_since(earlier) when earlier is later
  later.duration_since(earlier); // ok
  earlier.duration_since(later); // panic
  (Use checked_duration_since / saturating_duration_since to avoid panics.)

## Concurrency & Threads
* Poisoned mutexes don’t inherently panic, but calling .into_inner().unwrap() or blindly unwrap()-ing a poisoned lock result will.
* Panics in a spawned thread unwind that thread (join with JoinHandle::join() returns Err and unwrap() there will panic).

## Formatting & I/O Adjacent

* panic! from user Display/Debug impls during formatting
  (Not common, but if your implementation panics, formatting panics.)
* assert! inside closures used by library algorithms
  Sorting/comparison functions can panic if the comparator panics.

----

Notes & Tips
* Prefer non-panicking variants when inputs may be invalid:
  - get for indexing
  - checked_*/saturating_*/wrapping_* for arithmetic
  - try_reserve for capacity
  - checked_duration_since for time.
* Panics are defined behavior: they unwind by default (or abort if you set panic = "abort"), but they’re not UB.
* Many “panic if …” guarantees are documented at the method/operator site in the standard docs and the Reference.
