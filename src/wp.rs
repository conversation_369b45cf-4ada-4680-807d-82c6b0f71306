//! This module computes verification conditions (VCs) for each function in the
//! RustIR AST. VCs are computed via a weakest-precondition (WP) walk.
//!
//! High level:
//!   - We walk the IR *backwards*, threading a continuation-style postcondition
//!     (`ValuePost`) through expressions/statements/blocks.
//!   - Along the way we *emit obligations* (`Obligation`) for: function posts,
//!     call preconditions, loop preservation/exit, safety side conditions, user
//!     assertions, and (optionally) termination.
//!   - The resulting set of VCs can be proved externally (e.g., in Lean).
//!
//! Design notes:
//!   - Postconditions are represented as data (`ValuePost`) so they can be
//!     serialized; `apply_post(Q, t)` is syntactic substitution Q.body[v := t].
//!   - Calls consult `Sigma` for `pre`/`ens`, substitute actuals for formals,
//!     and produce the usual `Pre(args) ∧ ∀r. Ens(args,r) → Q[r]` shape.
//!   - Loops currently default to the invariant `⊤` (to be replaced by user-
//!     supplied invariants). We still emit preservation/exit VCs to surface the
//!     proof obligations even before invariants are provided.
//!
//! TODO (remaining):
//!   * Termination VCs: emit rank/variant obligations instead of `⊤` stub.
//!   * Richer safety: bounds for slices/arrays, aliasing/borrow checks, etc.
//!   * Spans: plumb source spans from IR nodes into `current_span` everywhere.

#![allow(dead_code)]

use std::collections::HashMap;

use crate::logic_core::{
    self as lc, apply_post, cast_ok, exhaustive, in_bounds, matches_pat, neq0, NameGen, Ty,
};
use crate::rust_ir::{Crate as IrCrate, Item};

use crate::rust_ir::{
    BinOp, Block, Expr, FunctionDef, Literal, Pattern, Stmt, TraitUfcsCall, Type, UnOp,
};

// ───────────────────────────── Specs: Σ ──────────────────────────────────────

/// Function specification used at *call sites* to generate VCs.
///
/// `params` are `(name, Ty)` pairs for formals.  
/// `ret` is `(ret_symbol, Ty)`; we conventionally use `"ret"`.  
/// `pre` and `ens` are logic formulas over formals and `ret_symbol`.
#[derive(Clone, Debug)]
pub struct FnSpec {
    pub params: Vec<(String, Ty)>,
    pub ret: (String, Ty),
    pub pre: lc::Formula,
    pub ens: lc::Formula, // mentions params and ret var
}

/// Fully qualified function name (`path::to::fn`). Keep as String for now.
type FqFuncName = String;

/// The specification environment Σ: name → function spec.
#[derive(Default, Clone, Debug)]
pub struct Sigma {
    pub fun: HashMap<FqFuncName, FnSpec>,
}

/// Why a particular VC exists (drives UX and proof routing).
#[derive(Clone, Debug)]
pub enum Reason {
    /// `Pre(args) ⇒ WP(body, Ens)` for the enclosing function.
    FunctionPost,
    /// A call required `callee`'s `pre` (with actuals substituted).
    CallPre { callee: String },
    /// Loop invariant holds initially, on continue, and on each iteration.
    LoopInit,   // not currently emitted; reserved for explicit init checks
    LoopPres,   // emitted: I ∧ cond ⇒ WP(body, I)
    LoopExit,   // emitted: I ∧ ¬cond ⇒ Q
    LoopTerm,   // optional termination VC (placeholder for now)
    /// Side conditions / safety checks.
    DivisionByZero,
    IndexOutOfBounds,
    InvalidCast,
    /// Exhaustiveness of pattern matching.
    MatchExhaustive,
    /// A `assert(p)` encountered in user code.
    UserAssert,
}

/// Optional source mapping for better diagnostics in UIs.
#[derive(Clone, Debug)]
pub struct SrcSpan {
    pub file: String,
    pub start: u32,
    pub end: u32,
}

/// One verification condition with reason, location, and the logic formula.
#[derive(Clone, Debug)]
pub struct Obligation {
    /// Why this VC exists (for humans / grouping).
    pub reason: Reason,
    /// Where it came from in the source (if available).
    pub span: Option<SrcSpan>,
    /// The proposition to be proved externally.
    pub formula: lc::Formula,
}

/// All VCs produced for a single function (convenient unit for emission).
#[derive(Clone, Debug)]
pub struct Bundle {
    pub function_name: String,
    pub vcs: Vec<Obligation>,
}

/// Context threaded through the WP walk.
///
/// Holds Σ, a fresh-name generator, the current function’s postcondition
/// as a `ValuePost`, and the bag of VCs we’ve emitted so far.
struct Ctx<'a> {
    /// The specification environment; holds all function specifications that
    /// may be called within the function we are currently verifying.
    sigma: &'a Sigma,
    /// A name generator for fresh variables (temps, ret witnesses, etc.).
    namegen: NameGen,
    /// The list of VCs generated so far.
    vcs: Vec<Obligation>,
    /// The source span of the current node (plumb from IR where available).
    current_span: Option<SrcSpan>,
    /// The postcondition for the current function (defunctionalized Q).
    return_post: lc::ValuePost,
    /// The name of the function in which the WP walk is occurring.
    current_fn: String,
}

impl<'a> Ctx<'a> {
    fn new(sigma: &'a Sigma, current_fn: String, return_post: lc::ValuePost) -> Self {
        Self {
            sigma,
            namegen: NameGen::default(),
            vcs: vec![],
            current_span: None,
            return_post,
            current_fn,
        }
    }

    /// Record a new VC with the current span (if any).
    fn push_vc(&mut self, reason: Reason, formula: lc::Formula) {
        self.vcs.push(Obligation {
            reason,
            span: self.current_span.clone(),
            formula,
        });
    }
}

// ─────────────────────────── Public entrypoints ──────────────────────────────

/// Options that tune which obligations are generated.
pub struct WpOptions {
    pub generate_termination_vcs: bool,
}

/// Compute all VCs for a single function definition.
///
/// Shape:
///   1) Build `return_post = {ret ↦ Ens}` from the function’s spec.
///   2) Compute `WP(body, return_post)` with `break/continue = ⊥` at top level.
///   3) Emit the *top-level* VC: `Pre ⇒ WP(body, Ens)`.
pub fn generate_function_vcs(f: &FunctionDef, sigma: &Sigma, opts: &WpOptions) -> Bundle {
    // Build return_post from the function's ensures
    let (params, ret_ty, pre, ens) = boundary_from_ir(f);
    let return_post = lc::ValuePost {
        v: "ret".into(),
        body: ens.clone(),
    };

    let mut ctx = Ctx::new(sigma, f.name.clone(), return_post.clone());

    // Top-level block (no break/continue allowed): break/cont posts = False (unreachable)
    let q_end = return_post;
    let body_wp = wp_block(
        &f.body,
        &q_end,
        &lc::Formula::False,
        &lc::Formula::False,
        &mut ctx,
        opts,
    );

    // Top VC for the function: caller’s obligation to meet the precondition.
    let top = lc::Formula::imp(pre, body_wp);
    ctx.push_vc(Reason::FunctionPost, top);

    Bundle {
        function_name: f.name.clone(),
        vcs: ctx.vcs,
    }
}

/// Extracts the boundary (`params`, return type, `pre`, and `post`) from IR.
/// Pre/Post are IR expressions; we translate them to logic formulas.
fn boundary_from_ir(f: &FunctionDef) -> (Vec<(String, Ty)>, Ty, lc::Formula, lc::Formula) {
    let params: Vec<(String, Ty)> = f
        .params
        .iter()
        .map(|(n, t)| (n.clone(), map_ty(t)))
        .collect();
    let ret_ty = f.return_type.as_ref().map(map_ty).unwrap_or(Ty::Unit);

    // Pre/Post are IR Exprs containing boolean propositions; translate to Formula.
    let pre = f
        .spec
        .pre
        .as_ref()
        .map(|e| expr_as_formula(e))
        .unwrap_or(lc::Formula::True);
    // Post mentions `ret` (convention). We'll treat it as formula over ret.
    let post_body = f
        .spec
        .post
        .as_ref()
        .map(|e| expr_as_formula(e))
        .unwrap_or(lc::Formula::True);

    (params, ret_ty, pre, post_body)
}

/// Minimal type translation from RustIR into the logic’s `Ty`.
/// Extend as your IR’s type surface expands.
fn map_ty(ir_ty: &Type) -> Ty {
    match ir_ty {
        Type::Path(segments) => Ty::User(segments.join("::")),
        Type::Tuple(ts) => Ty::Tuple(ts.iter().map(map_ty).collect()),
        Type::Int(_) => Ty::Int,
        Type::Bool => Ty::Bool,
        // Keep this as `unimplemented!` so unsupported types fail loudly during bring-up.
        _ => unimplemented!("unsupported type: {:?}", ir_ty.to_string()),
    }
}

// ──────────────── WP over Blocks / Statements / Expressions ─────────────────

/// WP for a block: process tail expression first (if any), then statements in
/// reverse order. `break_post` and `cont_post` are the continuation posts the
/// *enclosing loop* assigns to `break`/`continue` (⊥ at top level).
fn wp_block(
    block: &Block,
    q: &lc::ValuePost,
    break_post: &lc::Formula,
    cont_post: &lc::Formula,
    ctx: &mut Ctx,
    opts: &WpOptions,
) -> lc::Formula {
    // Tail expression (if present) behaves like `return tail;` only for function body;
    // within sub-blocks we just evaluate it as an expression to feed Q.
    let mut acc = q.body.clone();
    if let Some(tail) = &block.tail {
        acc = wp_expr(tail, q, ctx, opts);
    }
    for stmt in block.statements.iter().rev() {
        acc = wp_stmt(stmt, &acc, break_post, cont_post, ctx, opts);
    }
    acc
}

/// WP for a statement. We compute the precondition that guarantees `post`.
fn wp_stmt(
    stmt: &Stmt,
    post: &lc::Formula,
    break_post: &lc::Formula,
    cont_post: &lc::Formula,
    ctx: &mut Ctx,
    opts: &WpOptions,
) -> lc::Formula {
    match stmt {
        // let x = e;  ⇒  WP(e, v ↦ post[x := v])
        Stmt::Let {
            name,
            init: Some(e),
            ..
        } => {
            let v = ctx.namegen.fresh("t");
            let q = lc::ValuePost {
                v: v.clone(),
                body: lc::subst_formula(post, name, &lc::Term::Var(v.clone())),
            };
            wp_expr(e, &q, ctx, opts)
        }
        // let x;  ⇒  ∀x. post     (conservative approximation until you track init)
        Stmt::Let {
            name, init: None, ..
        } => lc::Formula::forall(vec![(name.clone(), Ty::User("_".into()))], post.clone()),

        // assert(p);  ⇒  emit VC `p` (for diagnostics), continue under p ∧ post
        Stmt::Assert(cond) => {
            let phi = expr_as_formula(cond);
            ctx.push_vc(Reason::UserAssert, phi.clone());
            lc::Formula::and(phi, post.clone())
        }

        // e;  ⇒  WP(e, _ ↦ post)  (evaluate for effects, discard value)
        Stmt::Expr(expr) => {
            let q = lc::ValuePost {
                v: ctx.namegen.fresh("_"),
                body: post.clone(),
            };
            wp_expr(expr, &q, ctx, opts)
        }

        // return e;  ⇒  WP(e, return_post)
        Stmt::Return(Some(e)) => {
            let rp = ctx.return_post.clone(); // avoid borrow conflict
            wp_expr(e, &rp, ctx, opts)
        }

        // return;  (unit) ⇒ apply return_post to ()
        Stmt::Return(None) => apply_post(&ctx.return_post, lc::Term::Tuple(vec![])),

        // Loop control: delegate to the enclosing loop’s continuation posts.
        Stmt::Break => break_post.clone(),
        Stmt::Continue => cont_post.clone(),
    }
}

/// WP for expressions. Each case builds the “value → post” link using `ValuePost`.
fn wp_expr(expr: &Expr, q: &lc::ValuePost, ctx: &mut Ctx, opts: &WpOptions) -> lc::Formula {
    match expr {
        // literals/paths directly substitute into Q
        Expr::Literal(lit) => apply_post(q, lit_to_term(lit)),
        Expr::Path(segments) => apply_post(q, lc::Term::Var(segments.join("::"))),

        // binary/unary ops
        Expr::Binary { op, lhs, rhs } => wp_binop(op, lhs, rhs, q, ctx, opts),
        Expr::Unary { op, expr } => {
            // Evaluate operand to fresh v, then apply operator as uninterpreted function.
            let v = ctx.namegen.fresh("t");
            let body = apply_post(
                q,
                lc::Term::App(unop_fun(op), vec![lc::Term::Var(v.clone())]),
            );
            wp_expr(expr, &lc::ValuePost { v, body }, ctx, opts)
        }

        // &e / &mut e: modeled as addr_of(_)
        Expr::AddressOf { mutable, expr } => {
            let v = ctx.namegen.fresh("t");
            let addr = lc::Term::App(
                if *mutable { "addr_of_mut" } else { "addr_of" },
                vec![lc::Term::Var(v.clone())],
            );
            let body = apply_post(q, addr);
            wp_expr(expr, &lc::ValuePost { v, body }, ctx, opts)
        }

        // e as T: require cast_ok(e,T), then pass cast(e,T) to Q
        Expr::Cast { expr, ty } => {
            let v = ctx.namegen.fresh("t");
            let casted = lc::Term::App(
                "cast",
                vec![
                    lc::Term::Var(v.clone()),
                    lc::Term::Var(map_ty(ty).to_string()),
                ],
            );
            let body = lc::Formula::and(
                cast_ok(lc::Term::Var(v.clone()), map_ty(ty)),
                apply_post(q, casted),
            );
            wp_expr(expr, &lc::ValuePost { v, body }, ctx, opts)
        }

        // base[idx]: require in_bounds(base,idx), then result is idx(base,idx)
        Expr::Index { target, index } => {
            let vb = ctx.namegen.fresh("b");
            let vi = ctx.namegen.fresh("i");
            let body = lc::Formula::and(
                in_bounds(lc::Term::Var(vb.clone()), lc::Term::Var(vi.clone())),
                apply_post(
                    q,
                    lc::Term::App(
                        "idx",
                        vec![lc::Term::Var(vb.clone()), lc::Term::Var(vi.clone())],
                    ),
                ),
            );
            let after_i = wp_expr(
                index,
                &lc::ValuePost {
                    v: vi.clone(),
                    body,
                },
                ctx,
                opts,
            );
            let after_b = wp_expr(
                target,
                &lc::ValuePost {
                    v: vb.clone(),
                    body: after_i,
                },
                ctx,
                opts,
            );
            // Also record an explicit VC so out-of-bounds errors are easy to point to.
            ctx.push_vc(
                Reason::IndexOutOfBounds,
                in_bounds(lc::Term::Var(vb), lc::Term::Var(vi)),
            );
            after_b
        }

        // e.field: model as field(e, "field")
        Expr::FieldAccess { target, field } => {
            let v = ctx.namegen.fresh("t");
            let fld = lc::Term::App(
                "field",
                vec![lc::Term::Var(v.clone()), lc::Term::Var(field.clone())],
            );
            let body = apply_post(q, fld);
            wp_expr(target, &lc::ValuePost { v, body }, ctx, opts)
        }

        // receiver.method(args…): evaluate receiver and args left-to-right, then treat as a call
        Expr::MethodCall {
            receiver,
            method,
            args,
        } => {
            // Evaluate receiver and args, then treat as call to `method` with receiver as first arg.
            // (We model it by looking up `method` in Σ using just the method name;
            // if you prefer trait-qualified resolution, adjust `call_with_temps` and Σ keys.)
            fn eval_chain<'e>(
                es: impl IntoIterator<Item = &'e Expr>,
                qbody: lc::Formula,
                ctx: &mut Ctx,
                opts: &WpOptions,
                names: &mut Vec<String>,
            ) -> lc::Formula {
                // Evaluate in left-to-right *runtime* order by folding backwards.
                let mut vec: Vec<&Expr> = es.into_iter().collect();
                vec.reverse();
                vec.into_iter().fold(qbody, |acc, e| {
                    let v = ctx.namegen.fresh("a");
                    names.push(v.clone());
                    wp_expr(e, &lc::ValuePost { v, body: acc }, ctx, opts)
                })
            }
            let mut temp_names: Vec<String> = vec![];
            let q_body = {
                // After evaluating, look up the method in Σ and build Pre/Ens obligations.
                let callee = method.clone();
                call_with_temps(&callee, &temp_names, q, ctx)
            };
            let after_args = eval_chain(args.iter(), q_body, ctx, opts, &mut temp_names);
            // Finally the receiver
            let after_recv = eval_chain(
                std::iter::once(receiver.as_ref()),
                after_args,
                ctx,
                opts,
                &mut temp_names.clone(),
            );
            after_recv
        }

        // f(args…): evaluate args left-to-right, then apply call rule if `f` resolves to a path
        Expr::Call { callee, args } => {
            let mut temp_names: Vec<String> = vec![];
            let q_body = {
                let callee_name = match &**callee {
                    Expr::Path(segs) => Some(segs.join("::")),
                    _ => None,
                };
                match callee_name {
                    Some(name) => call_with_temps(&name, &temp_names, q, ctx),
                    // Higher-order / unknown callee: pass an uninterpreted value to Q.
                    None => apply_post(q, lc::Term::Var("_call".into())),
                }
            };
            // Evaluate args left-to-right by folding backwards.
            let acc = args.iter().rev().fold(q_body, |acc, e| {
                let v = ctx.namegen.fresh("a");
                temp_names.push(v.clone());
                wp_expr(e, &lc::ValuePost { v, body: acc }, ctx, opts)
            });
            // Evaluate callee expression last (if not a simple path), preserving order.
            match &**callee {
                Expr::Path(_) => acc,
                other => {
                    let v = ctx.namegen.fresh("f");
                    wp_expr(other, &lc::ValuePost { v, body: acc }, ctx, opts)
                }
            }
        }

        // if cond { then } else { else }  ⇒  (cond ⇒ WP(then,Q)) ∧ (¬cond ⇒ WP(else,Q))
        // We evaluate cond first to a fresh boolean `cv` and then reference it.
        Expr::If {
            cond,
            then_branch,
            else_branch,
        } => {
            let cv = ctx.namegen.fresh("c");
            let then_wp = wp_block(
                then_branch,
                q,
                &lc::Formula::False,
                &lc::Formula::False,
                ctx,
                opts,
            );
            let else_wp = wp_block(
                else_branch.as_ref().map(|b| b.as_ref()).unwrap_or(&Block {
                    statements: vec![],
                    tail: None,
                }),
                q,
                &lc::Formula::False,
                &lc::Formula::False,
                ctx,
                opts,
            );
            let branch = lc::Formula::and(
                lc::Formula::imp(
                    lc::Formula::eq(lc::Term::Var(cv.clone()), lc::Term::lit_b(true)),
                    then_wp,
                ),
                lc::Formula::imp(
                    lc::Formula::eq(lc::Term::Var(cv.clone()), lc::Term::lit_b(false)),
                    else_wp,
                ),
            );
            wp_expr(
                cond,
                &lc::ValuePost {
                    v: cv,
                    body: branch,
                },
                ctx,
                opts,
            )
        }

        // while cond { body }  ⇒  see `wp_while` (emits Pres/Exit VCs; WP result is the invariant).
        Expr::While { cond, body } => wp_while(cond, body, q, ctx, opts),

        // loop { body }  ⇒  like while-true; require invariant to hold/progress (see `wp_loop`).
        Expr::Loop(body) => wp_loop(body, q, ctx, opts),

        // match scrut { pat_i => block_i }  ⇒  ∧_i (matches(scrut,pat_i) ⇒ WP(block_i,Q)) and exhaustiveness VC
        Expr::Match { expr: scrut, arms } => wp_match(scrut, arms, q, ctx, opts),

        // `{…}` as an expression: reuse `wp_block`
        Expr::BlockExpr(b) => wp_block(b, q, &lc::Formula::False, &lc::Formula::False, ctx, opts),

        // Trait UFCS call: treat like a call to `TraitPath::method`
        Expr::TraitUfcsCallExpr(TraitUfcsCall {
            trait_path,
            method,
            args,
            ..
        }) => {
            let name = trait_path.join("::") + "::" + method.as_str();
            let mut temp_names: Vec<String> = vec![];
            let q_body = call_with_temps(&name, &temp_names, q, ctx);
            args.iter().rev().fold(q_body, |acc, e| {
                let v = ctx.namegen.fresh("a");
                temp_names.push(v.clone());
                wp_expr(e, &lc::ValuePost { v, body: acc }, ctx, opts)
            })
        }
    }
}

/// Apply the *call rule* after arguments have been evaluated to `temps`.
///
/// If Σ contains a spec for `callee`, emit a `CallPre` VC (precondition with
/// actuals substituted) and return `∀r. Ens(actuals,r) → Q[r]`. Otherwise,
/// pass an uninterpreted return to `Q`.
fn call_with_temps(
    callee: &str,
    temps: &[String],
    q: &lc::ValuePost,
    ctx: &mut Ctx,
) -> lc::Formula {
    if let Some(spec) = ctx.sigma.fun.get(callee) {
        // Precondition VC
        let actuals: Vec<lc::Term> = temps.iter().map(|v| lc::Term::Var(v.clone())).collect();
        let pre_inst = inst_params(&spec.pre, &spec.params, &actuals);
        ctx.push_vc(
            Reason::CallPre {
                callee: callee.to_string(),
            },
            pre_inst.clone(),
        );

        // Ensures → Q
        let r = ctx.namegen.fresh("r");
        let ens_r = lc::subst_formula(
            &inst_params(&spec.ens, &spec.params, &actuals),
            &spec.ret.0,
            &lc::Term::Var(r.clone()),
        );
        let implies_q = lc::Formula::imp(ens_r, apply_post(q, lc::Term::Var(r.clone())));
        lc::Formula::forall(vec![(r.clone(), spec.ret.1.clone())], implies_q)
    } else {
        // Unknown callee: just pass through as uninterpreted
        apply_post(q, lc::Term::Var("_ret".into()))
    }
}

/// Substitute formals with actual terms in a spec formula.
fn inst_params(phi: &lc::Formula, formals: &[(String, Ty)], actuals: &[lc::Term]) -> lc::Formula {
    let mut out = phi.clone();
    for ((x, _), a) in formals.iter().zip(actuals.iter()) {
        out = lc::subst_formula(&out, x, a);
    }
    out
}

/// WP for `while` with invariant `I` (currently `⊤`).
///
/// Emits:
///   - Preservation: `I ∧ cond ⇒ WP(body, I)`
///   - Exit:         `I ∧ ¬cond ⇒ Q`
///   - (optional) Termination: placeholder VC
///
/// The WP of the `while` expression itself is the invariant `I`.
fn wp_while(
    cond: &Expr,
    body: &Block,
    q: &lc::ValuePost,
    ctx: &mut Ctx,
    opts: &WpOptions,
) -> lc::Formula {
    // Use an invariant variable I (users may attach one later via attributes; default True)
    let inv = lc::Formula::True;

    // Preservation: I ∧ cond ⇒ wp(body, I) with continue mapping to I and break mapping to Q
    let body_wp = {
        let inv_post = lc::ValuePost {
            v: ctx.namegen.fresh("_"),
            body: inv.clone(),
        };
        wp_block(body, &inv_post, &q.body, &inv, ctx, opts)
    };
    let pres = lc::Formula::imp(
        lc::Formula::and(inv.clone(), expr_as_formula(cond)),
        body_wp,
    );
    ctx.push_vc(Reason::LoopPres, pres);

    // Exit: I ∧ ¬cond ⇒ Q
    let exit = lc::Formula::imp(
        lc::Formula::and(inv.clone(), lc::Formula::not(expr_as_formula(cond))),
        q.body.clone(),
    );
    ctx.push_vc(Reason::LoopExit, exit);

    if opts.generate_termination_vcs {
        ctx.push_vc(Reason::LoopTerm, lc::Formula::True); // placeholder for ranking function VC
    }

    inv
}

/// WP for `loop { … }` as `while true { … }` with an invariant `I` (currently `⊤`).
fn wp_loop(body: &Block, q: &lc::ValuePost, ctx: &mut Ctx, opts: &WpOptions) -> lc::Formula {
    // Infinite loop requires an invariant; default to True and emit preservation VC
    let inv = lc::Formula::True;
    let body_wp = {
        let inv_post = lc::ValuePost {
            v: ctx.namegen.fresh("_"),
            body: inv.clone(),
        };
        wp_block(body, &inv_post, &q.body, &inv, ctx, opts)
    };
    ctx.push_vc(Reason::LoopPres, body_wp);
    if opts.generate_termination_vcs {
        ctx.push_vc(Reason::LoopTerm, lc::Formula::True);
    }
    inv
}

/// WP for `match`:
///   ∧_i (matches(scrut, pat_i) ⇒ WP(block_i, Q))
/// and an exhaustiveness VC (currently: `exhaustive(scrut)` placeholder).
fn wp_match(
    scrut: &Expr,
    arms: &Vec<(Pattern, Box<Block>)>,
    q: &lc::ValuePost,
    ctx: &mut Ctx,
    opts: &WpOptions,
) -> lc::Formula {
    let sv = ctx.namegen.fresh("v");
    let scrut_term = lc::Term::Var(sv.clone());

    // For each arm, build: matches(p, v) ⇒ wp(block, q)
    let mut conj: Option<lc::Formula> = None;
    for (pat, block) in arms {
        let branch_wp = wp_block(
            block,
            q,
            &lc::Formula::False,
            &lc::Formula::False,
            ctx,
            opts,
        );
        let arm = lc::Formula::imp(
            matches_pat(scrut_term.clone(), pattern_as_term(pat)),
            branch_wp,
        );
        conj = Some(match conj {
            None => arm,
            Some(acc) => lc::Formula::and(acc, arm),
        });
    }

    // Add exhaustiveness VC (external checker can refine)
    ctx.push_vc(
        Reason::MatchExhaustive,
        exhaustive(vec![scrut_term.clone()]),
    );

    let body = conj.unwrap_or(lc::Formula::True);
    wp_expr(scrut, &lc::ValuePost { v: sv, body }, ctx, opts)
}

// ─────────────────────────────── Helpers ─────────────────────────────────────

/// Translate a *boolean* RustIR expression into a logic formula.
/// Only a restricted subset is supported; non-boolean forms return `⊥` by design.
fn expr_as_formula(e: &Expr) -> lc::Formula {
    match e {
        Expr::Literal(Literal::Bool(true)) => lc::Formula::True,
        Expr::Literal(Literal::Bool(false)) => lc::Formula::False,
        Expr::Binary { op, lhs, rhs } => match op {
            // Propositional connectives (short-circuiting at runtime, but in logic they're plain).
            BinOp::And => lc::Formula::and(expr_as_formula(lhs), expr_as_formula(rhs)),
            BinOp::Or => lc::Formula::or(expr_as_formula(lhs), expr_as_formula(rhs)),
            // Equality/inequality and order comparisons over translated terms.
            BinOp::Eq => lc::Formula::eq(expr_as_term(lhs), expr_as_term(rhs)),
            BinOp::NotEq => lc::Formula::not(lc::Formula::eq(expr_as_term(lhs), expr_as_term(rhs))),
            BinOp::Lt | BinOp::Le | BinOp::Gt | BinOp::Ge => {
                lc::Formula::pred(binop_sym(op), vec![expr_as_term(lhs), expr_as_term(rhs)])
            }
            _ => lc::Formula::False,
        },
        Expr::Unary {
            op: UnOp::Not,
            expr,
        } => lc::Formula::not(expr_as_formula(expr)),
        // Treat bare paths in a spec as booleans equated to `true` (common idiom).
        Expr::Path(v) => lc::Formula::eq(lc::Term::Var(v.join("::")), lc::Term::lit_b(true)),
        _ => lc::Formula::False,
    }
}

/// Translate a general expression into a symbolic term (for use inside formulas).
/// We keep operators as uninterpreted function symbols; arithmetic reasoning is
/// then provided by the proving environment via interpretation lemmas/tactics.
fn expr_as_term(e: &Expr) -> lc::Term {
    match e {
        Expr::Literal(Literal::Int(n)) => lc::Term::lit_i(*n as i128),
        Expr::Literal(Literal::Bool(b)) => lc::Term::lit_b(*b),
        Expr::Path(segs) => lc::Term::Var(segs.join("::")),
        Expr::Binary { op, lhs, rhs } => {
            lc::Term::App(binop_sym(op), vec![expr_as_term(lhs), expr_as_term(rhs)])
        }
        Expr::Unary { op, expr } => lc::Term::App(unop_fun(op), vec![expr_as_term(expr)]),
        Expr::FieldAccess { target, field } => lc::Term::App(
            "field",
            vec![expr_as_term(target), lc::Term::Var(field.clone())],
        ),
        Expr::Index { target, index } => {
            lc::Term::App("idx", vec![expr_as_term(target), expr_as_term(index)])
        }
        Expr::Cast { expr, ty } => lc::Term::App(
            "cast",
            vec![expr_as_term(expr), lc::Term::Var(map_ty(ty).to_string())],
        ),
        _ => lc::Term::Var("_e".into()),
    }
}

/// Literals→terms (conservative; extend as new literal kinds appear).
fn lit_to_term(l: &Literal) -> lc::Term {
    match l {
        Literal::Int(n) => lc::Term::lit_i(*n as i128),
        Literal::Bool(b) => lc::Term::lit_b(*b),
        _ => lc::Term::Var("_lit".into()),
    }
}

/// Encode patterns as terms for the uninterpreted predicate `matches(v, pat)`.
fn pattern_as_term(p: &Pattern) -> lc::Term {
    // Encode patterns as terms for an uninterpreted matches( v, pat ) predicate
    match p {
        Pattern::Wildcard => lc::Term::Var("_".into()),
        Pattern::Ident(x) => lc::Term::Var(x.clone()),
        Pattern::Literal(Literal::Int(n)) => lc::Term::lit_i(*n as i128),
        Pattern::Literal(Literal::Bool(b)) => lc::Term::lit_b(*b),
        Pattern::Tuple(ps) => lc::Term::Tuple(ps.iter().map(pattern_as_term).collect()),
        Pattern::Struct { path, fields } => {
            let mut fs = Vec::new();
            for (n, p) in fields {
                fs.push(lc::Term::App(
                    "field",
                    vec![lc::Term::Var(n.clone()), pattern_as_term(p)],
                ));
            }
            lc::Term::App(
                "struct",
                vec![lc::Term::Var(path.join("::")), lc::Term::Tuple(fs)],
            )
        }
        Pattern::TupleStruct { path, elems } => lc::Term::App(
            "tuple_struct",
            vec![
                lc::Term::Var(path.join("::")),
                lc::Term::Tuple(elems.iter().map(pattern_as_term).collect()),
            ],
        ),
        // Keep this explicit so unsupported constructs fail loudly during bring-up.
        _ => unimplemented!("unsupported pattern: {:?}", p.to_string()),
    }
}

/// Map binary operator to its symbolic function name used in `Term::App`.
fn binop_sym(op: &BinOp) -> &'static str {
    match op {
        BinOp::Add => "+",
        BinOp::Sub => "-",
        BinOp::Mul => "*",
        BinOp::Div => "/",
        BinOp::Rem => "%",
        BinOp::And => "&&",
        BinOp::Or => "||",
        BinOp::Eq => "==",
        BinOp::NotEq => "!=",
        BinOp::Lt => "<",
        BinOp::Le => "<=",
        BinOp::Gt => ">",
        BinOp::Ge => ">=",
        _ => unimplemented!("unsupported binary operation: {:?}", op.to_string()),
    }
}

/// Map unary operator to its symbolic function name used in `Term::App`.
fn unop_fun(op: &UnOp) -> &'static str {
    match op {
        UnOp::Neg => "neg",
        UnOp::Not => "!",
        UnOp::Deref => "deref",
    }
}

/// Binary-expression WP, respecting Rust’s left-to-right runtime evaluation.
///
/// We evaluate RHS first in the *backwards* traversal (so at runtime it’s LHS
/// first), thread its pre into the LHS, and finally apply the operator to the
/// two temps `v1` and `v2`. Division/remainder emit a `denom ≠ 0` VC.
fn wp_binop(
    op: &BinOp,
    lhs: &Expr,
    rhs: &Expr,
    q: &lc::ValuePost,
    ctx: &mut Ctx,
    opts: &WpOptions,
) -> lc::Formula {
    let v1 = ctx.namegen.fresh("t");
    let v2 = ctx.namegen.fresh("t");
    let op_app = lc::Term::App(
        binop_sym(op),
        vec![lc::Term::Var(v1.clone()), lc::Term::Var(v2.clone())],
    );
    let mut body = apply_post(q, op_app);
    match op {
        BinOp::Div | BinOp::Rem => {
            // Side-condition: right operand must be nonzero.
            body = lc::Formula::and(neq0(lc::Term::Var(v2.clone())), body);
            ctx.push_vc(Reason::DivisionByZero, neq0(lc::Term::Var(v2.clone())));
        }
        _ => {}
    }
    // Backwards threading: RHS then LHS so runtime order is LHS then RHS.
    let after_rhs = wp_expr(
        rhs,
        &lc::ValuePost {
            v: v2.clone(),
            body,
        },
        ctx,
        opts,
    );
    let after_lhs = wp_expr(
        lhs,
        &lc::ValuePost {
            v: v1.clone(),
            body: after_rhs,
        },
        ctx,
        opts,
    );
    after_lhs
}

// ─────────────────────── Σ construction & driver hooks ───────────────────────

/// Build Σ by scanning the IR crate for function and method specs.
/// This lets call sites fetch `pre`/`ens` without peeking at bodies.
pub fn build_sigma_from_crate(krate: &IrCrate) -> Sigma {
    let mut sigma = Sigma::default();
    for item in &krate.items {
        // assumes `Crate { items: Vec<Item> }`
        match item {
            Item::Function(f) => {
                // Build from FunctionDef.spec
                let params: Vec<(String, Ty)> = f
                    .params
                    .iter()
                    .map(|(n, t)| (n.clone(), map_ty(t)))
                    .collect();
                let ret_ty = f.return_type.as_ref().map(map_ty).unwrap_or(Ty::Unit);
                let pre = f
                    .spec
                    .pre
                    .as_ref()
                    .map(expr_as_formula)
                    .unwrap_or(lc::Formula::True);
                let ens = f
                    .spec
                    .post
                    .as_ref()
                    .map(expr_as_formula)
                    .unwrap_or(lc::Formula::True);
                sigma.fun.insert(
                    f.name.clone(),
                    FnSpec {
                        params,
                        ret: ("ret".into(), ret_ty),
                        pre,
                        ens,
                    },
                );
            }
            Item::Impl(impl_def) => {
                // Pull method specs if you store them on impl methods similarly.
                for method in impl_def.items.iter() {
                    if let crate::rust_ir::ImplItem::Method(f) = method {
                        let fq = format!("{}::{}", impl_def.self_ty, f.name);
                        let params: Vec<(String, Ty)> = f
                            .params
                            .iter()
                            .map(|(n, t)| (n.clone(), map_ty(t)))
                            .collect();
                        let ret_ty = f.return_type.as_ref().map(map_ty).unwrap_or(Ty::Unit);
                        let pre = f
                            .spec
                            .pre
                            .as_ref()
                            .map(expr_as_formula)
                            .unwrap_or(lc::Formula::True);
                        let ens = f
                            .spec
                            .post
                            .as_ref()
                            .map(expr_as_formula)
                            .unwrap_or(lc::Formula::True);
                        sigma.fun.insert(
                            fq,
                            FnSpec {
                                params,
                                ret: ("ret".into(), ret_ty),
                                pre,
                                ens,
                            },
                        );
                    }
                }
            }
            _ => {}
        }
    }
    sigma
}

/// Iterate over all top-level functions and impl methods to produce VC bundles.
pub fn generate_vcs_for_crate(krate: &IrCrate, sigma: &Sigma, opts: &WpOptions) -> Vec<Bundle> {
    let mut out = Vec::new();
    for item in &krate.items {
        if let Item::Function(f) = item {
            out.push(generate_function_vcs(f, sigma, opts));
        }
        if let Item::Impl(impl_def) = item {
            for method in impl_def.items.iter() {
                if let crate::rust_ir::ImplItem::Method(f) = method {
                    out.push(generate_function_vcs(f, sigma, opts));
                }
            }
        }
    }
    out
}

// ─────────────────────────── Pretty printing ─────────────────────────────────

use std::fmt::{self, Write as _};

impl fmt::Display for Reason {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Reason::FunctionPost => write!(f, "FunctionPost"),
            Reason::CallPre { .. } => write!(f, "CallPre"),
            Reason::LoopInit => write!(f, "LoopInit"),
            Reason::LoopPres => write!(f, "LoopPres"),
            Reason::LoopExit => write!(f, "LoopExit"),
            Reason::LoopTerm => write!(f, "LoopTerm"),
            Reason::DivisionByZero => write!(f, "DivisionByZero"),
            Reason::IndexOutOfBounds => write!(f, "IndexOutOfBounds"),
            Reason::InvalidCast => write!(f, "InvalidCast"),
            Reason::MatchExhaustive => write!(f, "MatchExhaustive"),
            Reason::UserAssert => write!(f, "UserAssert"),
        }
    }
}

fn fmt_span(span: &Option<SrcSpan>) -> String {
    match span {
        Some(s) => format!("{} [{}, {})", s.file, s.start, s.end),
        None => "unknown".to_string(),
    }
}

// Pretty-print Terms (lightweight; extend as needed).
fn fmt_term(t: &lc::Term) -> String {
    use lc::Term::*;
    match t {
        Var(s) => s.clone(),
        LitI(n) => n.to_string(),
        LitB(b) => b.to_string(),
        Tuple(ts) => {
            let inner = ts.iter().map(fmt_term).collect::<Vec<_>>().join(", ");
            format!("({inner})")
        }
        App(f, args) => {
            // Common structured renderings first
            match (f.as_str(), args.as_slice()) {
                ("+", [a, b]) => format!("({} + {})", fmt_term(a), fmt_term(b)),
                ("-", [a, b]) => format!("({} - {})", fmt_term(a), fmt_term(b)),
                ("*", [a, b]) => format!("({} * {})", fmt_term(a), fmt_term(b)),
                ("/", [a, b]) => format!("({} / {})", fmt_term(a), fmt_term(b)),
                ("%", [a, b]) => format!("({} % {})", fmt_term(a), fmt_term(b)),
                ("<",  [a, b]) => format!("{} < {}",  fmt_term(a), fmt_term(b)),
                ("<=", [a, b]) => format!("{} <= {}", fmt_term(a), fmt_term(b)),
                (">",  [a, b]) => format!("{} > {}",  fmt_term(a), fmt_term(b)),
                (">=", [a, b]) => format!("{} >= {}", fmt_term(a), fmt_term(b)),
                ("idx", [b, i]) => format!("{}[{}]", fmt_term(b), fmt_term(i)),
                ("field", [t, Var(name)]) => format!("{}.{}", fmt_term(t), name),
                ("cast", [x, Var(ty)]) => format!("({} as {})", fmt_term(x), ty),
                ("deref", [x]) => format!("*{}", fmt_term(x)),
                ("neg", [x]) => format!("-{}", fmt_term(x)),
                ("!", [x]) => format!("!{}", fmt_term(x)),
                // Fallback: f(arg1, arg2, …)
                _ => {
                    let inner = args.iter().map(fmt_term).collect::<Vec<_>>().join(", ");
                    format!("{f}({inner})")
                }
            }
        }
    }
}

// Pretty-print Formulas with simple precedence/parentheses.
fn fmt_formula(phi: &lc::Formula) -> String {
    use lc::Formula::*;
    match phi {
        True => "true".into(),
        False => "false".into(),
        Eq(a, b) => format!("{} == {}", fmt_term(a), fmt_term(b)),
        Not(p) => format!("NOT {}", wrap(fmt_formula(p))),
        And(p, q) => format!("({} AND {})", fmt_formula(p), fmt_formula(q)),
        Or(p, q) => format!("({} OR {})", fmt_formula(p), fmt_formula(q)),
        Imp(p, q) => format!("({}) => ({})", fmt_formula(p), fmt_formula(q)),
        Pred(name, args) => {
            match (name.as_str(), args.as_slice()) {
                // Helpful shorthands for common side-conditions
                ("neq0", [x]) => format!("{} != 0", fmt_term(x)),
                ("in_bounds", [b, i]) => format!("in_bounds({}, {})", fmt_term(b), fmt_term(i)),
                ("cast_ok", [x, ty]) => format!("cast_ok({}, {})", fmt_term(x), fmt_term(ty)),
                ("matches", [v, p]) => format!("matches({}, {})", fmt_term(v), fmt_term(p)),
                ("exhaustive", [v]) => format!("exhaustive({})", fmt_term(v)),
                // General predicate
                _ => {
                    let inner = args.iter().map(fmt_term).collect::<Vec<_>>().join(", ");
                    format!("{name}({inner})")
                }
            }
        }
        ForAll(xs, body) => {
            let binders = xs.iter().map(|(x, _ty)| x.as_str()).collect::<Vec<_>>().join(", ");
            format!("forall {binders}. {}", fmt_formula(body))
        }
        Exists(xs, body) => {
            let binders = xs.iter().map(|(x, _ty)| x.as_str()).collect::<Vec<_>>().join(", ");
            format!("exists {binders}. {}", fmt_formula(body))
        }
    }
}

// Helper: wrap with parentheses if it helps readability in prefix contexts.
fn wrap(s: String) -> String {
    if s.starts_with('(') && s.ends_with(')') { s } else { format!("({s})") }
}

/// Pretty-print a collection of VC bundles as YAML-like blocks.
///
/// Example output:
/// ```text
/// bundle:
///   reason: FunctionPost
///   span: some_file.rs [23, 48)
///   formula: (0 <= a AND b <= a) => 0 <= ret
///
/// bundle:
///   reason: CallPre
///   span: some_file.rs [276, 285)
///   formula: 0 <= 5 AND 5 < 256
/// ```
pub fn pretty_print_bundles(bundles: &[Bundle]) -> String {
    let mut out = String::new();
    for bundle in bundles {
        for vc in &bundle.vcs {
            let _ = writeln!(out, "bundle:");
            let _ = writeln!(out, "  reason: {}", vc.reason);
            let _ = writeln!(out, "  span: {}", fmt_span(&vc.span));
            let _ = writeln!(out, "  formula: {}", fmt_formula(&vc.formula));
            let _ = writeln!(out);
        }
    }
    out
}

