// src/hir_analysis.rs

// Todo:
//   - If you also want fully qualified ADT paths (including crate name), you
//     can extend adt_path_segments by grabbing the crate from the module and
//     optionally prefixing:
//       // inside adt_path_segments
//       if let Some(m) = module {
//           let krate = m.krate();
//           // `display_name` lives in `ide` helpers on some versions; to avoid version drift we skip it here.
//           // If available in your version:
//           // if let Some(dn) = krate.display_name(db) {
//           //     segs.insert(0, dn.canonical_name().to_string());
//           // }
//       }
//   - Rewrite rules:
//     - e1 += e2  =>  std::ops::AddAssign::add_assign(&mut e1, e2)
//       - expand inline for primitive types
//     - Consider using
// Open questions:
//   - Should we inline `?` desugaring?
//   - Should assignment expression be converted into `let` bindings?

use anyhow::Result;
use core::panic;
use std::path::Path;

use crate::rust_ir as ir;
use crate::rust_ir::{
    self, ConstantDef as Ir<PERSON><PERSON><PERSON>, Crate as Ir<PERSON><PERSON>, EnumDef as IrEnum, FunctionDef as Ir<PERSON>unction,
    FunctionSpec, Item as IrItem, StructDef as IrStruct, TypeAliasDef as IrTypeAlias,
    VariantDef as IrVariant, VariantPayload,
};
use ra_ap_ide::{AnalysisHost, Semantics};
use ra_ap_ide_db::RootDatabase;
use ra_ap_syntax::ast::{
    self, AstNode, HasArgList, HasAttrs, HasGenericParams, HasModuleItem, HasName,
};
use ra_ap_syntax::{Edition, SourceFile};

// use ra_ap_hir::{Adt, Mutability, Type as HirType, HasCrate};
use ra_ap_hir::{
    Adt, Const, Crate as HirCrate, Function, GenericDef, GenericParam as HirGenericParam, HasCrate,
    HasSource, HirDisplay, Module, ModuleSource, Mutability, Type as HirType,
    TypeAlias as HirTypeAlias,
};

pub fn analyze_workspace(project_root: &Path) -> Result<IrCrate> {
    // 1) Build RA DB from Cargo (manifest path handled in loader)
    let (db, _vfs) = crate::ra_loader::load_workspace_db(project_root, /*proc-macros*/ false)?;

    // 2) Host + DB ref for Semantics
    let host = AnalysisHost::with_database(db);
    let db_ref: &RootDatabase = host.raw_database();
    let sema = Semantics::new(db_ref);

    // 3) Walk all crates/modules via HIR
    let mut ir = IrCrate::new();

    let crates: Vec<HirCrate> = HirCrate::all(db_ref).into_iter().collect();

    for krate in crates {
        let root_mod = krate.root_module(); // .root_module(db_ref);
        visit_module(&sema, root_mod, &mut ir);
    }

    Ok(ir)
}

fn visit_module(sema: &Semantics<RootDatabase>, module: Module, ir: &mut IrCrate) {
    let db = sema.db;

    // Get the module's source (either a SourceFile or an inline Module node)
    let src = module.definition_source(db); // InFile<ModuleSource>

    // Use the same DB to parse/expand this file
    let _root = sema.parse_or_expand(src.file_id);

    match src.value {
        ModuleSource::SourceFile(sf) => {
            // SourceFile has top-level items
            for item in sf.items() {
                push_item(sema, item, ir);
            }
        }
        ModuleSource::Module(m) => {
            // Inline `mod foo { ... }` form, read from its item list
            if let Some(items) = m.item_list() {
                for item in items.items() {
                    push_item(sema, item, ir);
                }
            }
        }
        ModuleSource::BlockExpr(_) => {
            // Not possible for top-level module source
            unimplemented!("Top-level module as block expression");
        }
    }

    // Recurse into children modules
    for child in module.children(db) {
        visit_module(sema, child, ir);
    }
}

fn push_item(sema: &Semantics<RootDatabase>, item: ast::Item, ir: &mut IrCrate) {
    match item {
        ast::Item::Fn(fn_ast) => {
            if let Some(func) = sema.to_def(&fn_ast) {
                ir.items
                    .push(IrItem::Function(convert_function(sema, &func)));
            } else {
                ir.items
                    .push(IrItem::Function(convert_function_syntax_fallback(&fn_ast)));
            }
        }
        ast::Item::Struct(s_ast) => {
            if let Some(s) = sema.to_def(&s_ast) {
                ir.items.push(IrItem::Struct(convert_struct(sema, &s)));
            } else {
                ir.items
                    .push(IrItem::Struct(convert_struct_syntax_fallback(&s_ast)));
            }
        }
        ast::Item::Enum(e_ast) => {
            if let Some(e) = sema.to_def(&e_ast) {
                ir.items.push(IrItem::Enum(convert_enum(sema, &e)));
            } else {
                ir.items
                    .push(IrItem::Enum(convert_enum_syntax_fallback(&e_ast)));
            }
        }
        ast::Item::Const(c_ast) => {
            if let Some(c) = sema.to_def(&c_ast) {
                ir.items.push(IrItem::Constant(convert_const(sema, &c)));
            } else {
                ir.items
                    .push(IrItem::Constant(convert_const_syntax_fallback(&c_ast)));
            }
        }
        ast::Item::TypeAlias(t_ast) => {
            if let Some(t) = sema.to_def(&t_ast) {
                ir.items
                    .push(IrItem::TypeAlias(convert_type_alias(sema, &t)));
            } else {
                ir.items
                    .push(IrItem::TypeAlias(convert_type_alias_syntax_fallback(
                        &t_ast,
                    )));
            }
        }
        // TODO: traits, impls, use, extern crate, mods, etc.
        _ => {
            eprintln!(
                "DEBUG: skipping unsupported item kind {:?}",
                item.syntax().kind()
            );
        }
    }
}

// --- Converters for items --- //

fn convert_function(sema: &Semantics<RootDatabase>, fnk: &Function) -> IrFunction {
    let db = sema.db;
    let quoted_name = format!("{:?}", fnk.name(db).symbol());
    let name = quoted_name.trim_matches('\"').to_string();

    // Get function signature parameters - using correct API
    let params = fnk
        .assoc_fn_params(db)
        .iter()
        .map(|param| {
            // Extract parameter name and type from Local
            let param_name = param
                .name(db)
                .map(|n| format!("{:?}", n.symbol()))
                .map(|n| n.trim_matches('\"').to_string())
                .unwrap_or_else(|| "param".to_string());
            let param_type = convert_type(sema, &param.ty());
            (param_name, param_type)
        })
        .collect();

    // Get return type using correct API
    let return_type = {
        let ret_ty = fnk.ret_type(db);
        let ret_ty = ret_ty;
        if ret_ty.is_unit() {
            None
        } else {
            Some(convert_type(sema, &ret_ty))
        }
    };

    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*fnk).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();

    // HYBRID APPROACH: Extract function body with full type information
    let (spec, body) = if let Some(source) = fnk.source(sema.db) {
        let ast_fn = &source.value;
        let spec = extract_fn_spec_from_attrs(ast_fn);
        let body = if let Some(b) = ast_fn.body() {
            convert_ast_block_to_ir(&sema, &b) // still uses Semantics (same DB)
        } else {
            ir::Block {
                statements: vec![],
                tail: None,
            }
        };
        (spec, body)
    } else {
        (
            FunctionSpec {
                pre: None,
                post: None,
            },
            ir::Block {
                statements: vec![],
                tail: None,
            },
        )
    };

    IrFunction {
        name,
        generics,
        params,
        spec,
        return_type,
        body,
    }
}

fn convert_type_alias(sema: &Semantics<RootDatabase>, alias: &HirTypeAlias) -> IrTypeAlias {
    let db = sema.db;
    let name = format!("{:?}", alias.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*alias).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let aliased = convert_type(sema, &alias.ty(db));
    IrTypeAlias {
        name,
        generics,
        aliased,
    }
}

fn convert_struct(sema: &Semantics<RootDatabase>, strukt: &ra_ap_hir::Struct) -> IrStruct {
    let db = sema.db;
    let name = format!("{:?}", strukt.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*strukt).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let fields = strukt
        .fields(db)
        .iter()
        .map(|f| crate::rust_ir::StructField {
            name: format!("{:?}", f.name(db)),
            ty: convert_type(sema, &f.ty(db)),
        })
        .collect();
    IrStruct {
        name,
        generics,
        fields,
    }
}

fn convert_enum(sema: &Semantics<RootDatabase>, enm: &ra_ap_hir::Enum) -> IrEnum {
    let db = sema.db;
    let name = format!("{:?}", enm.name(db));
    // Get generics using GenericDef interface
    let generic_def: GenericDef = (*enm).into();
    let generics = generic_def
        .params(db)
        .iter()
        .map(|param| convert_generic_param(db, param))
        .collect();
    let variants = enm
        .variants(db)
        .iter()
        .map(|var| {
            // Determine variant payload by examining fields
            let fields = var.fields(db);
            let payload = if fields.is_empty() {
                VariantPayload::Unit
            } else {
                // For now, treat all non-empty variants as tuple variants
                // TODO: distinguish between tuple and struct variants
                let field_types = fields
                    .iter()
                    .map(|f| convert_type(sema, &f.ty(db)))
                    .collect();
                VariantPayload::Tuple(field_types)
            };
            IrVariant {
                name: format!("{:?}", var.name(db)),
                payload,
            }
        })
        .collect();
    IrEnum {
        name,
        generics,
        variants,
    }
}

fn convert_const(sema: &Semantics<RootDatabase>, c: &Const) -> IrConst {
    let db = sema.db;
    let name = c
        .name(db)
        .map(|n| format!("{:?}", n))
        .unwrap_or_else(|| "unnamed".to_string());
    let ty = convert_type(sema, &c.ty(db));
    // For now, use a placeholder expression - getting const expressions requires more work
    let expr = crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit);
    IrConst { name, ty, expr }
}

/// Convert HIR generic parameter to RustIR GenericParam
fn convert_generic_param(
    db: &ra_ap_ide_db::RootDatabase,
    param: &HirGenericParam,
) -> crate::rust_ir::GenericParam {
    use ra_ap_hir::GenericParam;

    match param {
        GenericParam::TypeParam(type_param) => {
            let name = format!("{:?}", type_param.name(db));
            // For now, simplified bounds handling
            let bounds = Vec::new(); // TODO: implement trait bounds conversion
            crate::rust_ir::GenericParam { name, bounds }
        }
        GenericParam::LifetimeParam(lifetime_param) => {
            let name = format!("{:?}", lifetime_param.name(db));
            crate::rust_ir::GenericParam {
                name,
                bounds: Vec::new(), // Lifetime parameters don't have trait bounds
            }
        }
        GenericParam::ConstParam(const_param) => {
            let name = format!("{:?}", const_param.name(db));
            crate::rust_ir::GenericParam {
                name,
                bounds: Vec::new(), // Const parameters don't have trait bounds in our IR
            }
        }
    }
}

/// Convert HIR types into RustIR Type\ // Updated implementation of convert_type
fn convert_type(sema: &Semantics<RootDatabase>, ty: &HirType) -> crate::rust_ir::Type {
    use crate::rust_ir::Type as Ir;

    let db = sema.db;

    // 1) Builtins & specials first
    if ty.is_unit() {
        return Ir::Tuple(vec![]); // ()
    }
    if ty.is_never() {
        return Ir::Never;
    }
    if ty.is_bool() {
        return Ir::Bool;
    }
    if ty.is_char() {
        return Ir::Char;
    }
    if ty.is_int_or_uint() {
        use ra_ap_ide::Crate as IdeCrate;
        // Keep the concrete i*/u* if we can; fall back to “int/uint” if needed.
        // `type_and_const_arguments` would stringify; here we just use a readable bucket.
        let hir_crate = ty.krate(sema.db);
        let ide_crate: IdeCrate = hir_crate.into();
        let target = ra_ap_hir_ty::display::DisplayTarget::from_crate(db, ide_crate);
        let ty_string = ty.display(db, target).to_string();
        let text = ty_string.as_str();
        match text {
            "i8" => return Ir::Int(rust_ir::IntPrecision::I8),
            "i16" => return Ir::Int(rust_ir::IntPrecision::I16),
            "i32" => return Ir::Int(rust_ir::IntPrecision::I32),
            "i64" => return Ir::Int(rust_ir::IntPrecision::I64),
            "i128" => return Ir::Int(rust_ir::IntPrecision::I128),
            "isize" => return Ir::Int(rust_ir::IntPrecision::Isize),
            "u8" => return Ir::Int(rust_ir::IntPrecision::U8),
            "u16" => return Ir::Int(rust_ir::IntPrecision::U16),
            "u32" => return Ir::Int(rust_ir::IntPrecision::U32),
            "u64" => return Ir::Int(rust_ir::IntPrecision::U64),
            "u128" => return Ir::Int(rust_ir::IntPrecision::U128),
            "usize" => return Ir::Int(rust_ir::IntPrecision::Usize),
            "f32" => return Ir::Float(rust_ir::FloatPrecision::F32),
            "f64" => return Ir::Float(rust_ir::FloatPrecision::F64),
            "f128" => return Ir::Float(rust_ir::FloatPrecision::F128),
            _ => panic!("Unknown int/uint type: {}", text),
        }
    }
    if ty.is_str() {
        return Ir::Path(vec!["str".into()]);
    }

    // 2) References
    if let Some((inner, mutbl)) = ty.as_reference() {
        return Ir::Reference {
            mutable: matches!(mutbl, Mutability::Mut),
            inner: Box::new(convert_type(sema, &inner)),
        };
    }

    // 3) Arrays / slices
    if let Some((elem, len)) = ty.as_array(db) {
        return Ir::Array {
            inner: Box::new(convert_type(sema, &elem)),
            len: Some(len),
        };
    }
    if let Some(elem) = ty.as_slice() {
        return Ir::Array {
            inner: Box::new(convert_type(sema, &elem)),
            len: None,
        };
    }

    // 4) Tuples
    if ty.is_tuple() {
        let elems = ty
            .tuple_fields(db)
            .into_iter()
            .map(|t| convert_type(sema, &t))
            .collect();
        return Ir::Tuple(elems);
    }

    // 5) Callable (fn pointers, closures after coercion, etc.)
    if let Some(call) = ty.as_callable(db) {
        let params = call
            .params()
            .into_iter()
            .map(|p| convert_type(sema, p.ty()))
            .collect::<Vec<_>>();
        let ret = Box::new(convert_type(sema, &call.return_type()));
        return Ir::FnPtr { params, ret };
    }

    // 6) Type parameter
    if let Some(tp) = ty.as_type_param(db) {
        return Ir::TypeParam(tp.name(db).symbol().to_string());
    }

    // 7) Associated type like `<T as Trait>::Assoc`
    if let Some(trate) = ty.as_associated_type_parent_trait(db) {
        //let (parent_ty, trait_, assoc_name) = trate;
        // let parent_ir = convert_type(sema, &parent_ty);
        // You could also encode the trait name in the path if you want: trait_.name(db)
        panic!("Associated type not implemented {:?}", trate);
        // let name = trate.name(db).symbol().to_string();
        // return Ir::AssocType { type_: Box::new(parent_ir), name: name };
    }

    // 8) ADTs (struct/enum/union) + type arguments
    if let Some(adt) = ty.as_adt() {
        let base = crate::rust_ir::Type::Path(adt_path_segments(db, adt));
        let args = ty
            .type_arguments()
            .map(|a| convert_type(sema, &a))
            .collect::<Vec<_>>();
        return if args.is_empty() {
            base
        } else {
            Ir::Generic {
                base: Box::new(base),
                args,
            }
        };
    }

    // 9) Dyn traits, impl trait, unknowns — make them readable but minimal
    if ty.is_unknown() {
        return Ir::Path(vec!["_".into()]);
    }
    if ty.is_closure() {
        // Closures can be coerced to fn ptr; keeping as opaque for now
        return Ir::Path(vec!["closure".into()]);
    }
    if let Some(_dyn) = ty.as_dyn_trait() {
        // Represent `dyn Trait<...>` as a path for now; you can elaborate by reading the trait & args.
        return Ir::Path(vec!["dyn".into(), "Trait".into()]);
    }

    // 10) Fallback: short, stable text (no giant TraitEnvironment dump)
    crate::rust_ir::Type::Path(vec![short_type_fallback(db, ty)])
}

// Helper: pick a short, deterministic name for ADTs
fn adt_path_segments(db: &RootDatabase, adt: Adt) -> Vec<String> {
    // Keep it simple and stable: module::...::Name
    // If you prefer *just* the base name, return vec![adt.name(db).to_string()]
    let (module, name) = match adt {
        Adt::Struct(s) => (Some(s.module(db)), s.name(db).symbol().to_string()),
        Adt::Enum(e) => (Some(e.module(db)), e.name(db).symbol().to_string()),
        Adt::Union(u) => (Some(u.module(db)), u.name(db).symbol().to_string()),
    };

    let mut segs = Vec::new();
    if let Some(m) = module {
        // Build `mod::submod::...` path; crate root has no name
        for m in m.path_to_root(db).into_iter().rev() {
            if let Some(n) = m.name(db) {
                segs.push(n.symbol().to_string());
            }
        }
    }
    segs.push(name);
    segs
}

// Helper: a concise fallback without the huge TraitEnvironment
fn short_type_fallback(_db: &RootDatabase, ty: &HirType) -> String {
    // `display` produces a pretty, user-facing string and is much shorter than `Debug`
    // use ra_ap_hir::HirDisplay;
    format!("{:?}", ty)
}

/// HYBRID APPROACH: Enhanced let statement conversion with full type extraction
fn convert_let_stmt_with_full_types(
    sema: &Semantics<RootDatabase>,
    let_stmt: &ra_ap_syntax::ast::LetStmt,
) -> crate::rust_ir::Stmt {
    // STEP 1: Extract name from AST (easy structure traversal)
    let name = if let Some(pat) = let_stmt.pat() {
        match pat {
            ra_ap_syntax::ast::Pat::IdentPat(ident) => ident
                .name()
                .map(|n| n.to_string())
                .unwrap_or_else(|| "unnamed".to_string()),
            _ => format!("{:?}", pat),
        }
    } else {
        "unnamed".to_string()
    };

    // STEP 2: Use Semantics to get the INFERRED type (this is the key!)
    let ty = if let Some(pat) = let_stmt.pat() {
        sema.type_of_pat(&pat).map(|type_info| {
            // Convert HIR type to our IR type - this gives us complete type info
            convert_type(sema, &type_info.original)
        })
    } else {
        None
    };

    // STEP 3: Process initializer with hybrid approach
    let init = let_stmt.initializer().map(|init_expr| {
        // We can recursively apply the hybrid approach
        // AST structure + Semantics type info for the initializer too
        let _init_type = sema.type_of_expr(&init_expr); // Full type info available!

        // For now, return a placeholder - would recurse with convert_ast_expr_with_types
        crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit)
    });

    crate::rust_ir::Stmt::Let { name, ty, init }
}

/// Convert AST patterns into RustIR Pattern
fn convert_ast_pattern(
    _sema: &Semantics<RootDatabase>,
    pat: &ra_ap_syntax::ast::Pat,
) -> crate::rust_ir::Pattern {
    use ra_ap_syntax::ast::Pat;

    match pat {
        Pat::IdentPat(ident) => {
            if let Some(name) = ident.name() {
                crate::rust_ir::Pattern::Ident(name.to_string())
            } else {
                crate::rust_ir::Pattern::Wildcard
            }
        }
        Pat::WildcardPat(_) => crate::rust_ir::Pattern::Wildcard,
        Pat::LiteralPat(lit_pat) => {
            if let Some(literal) = lit_pat.literal() {
                // Use the same approach as convert_ast_expr_with_types for literals
                let text = literal.token().text().to_string();
                let ir_lit = if text.starts_with('"') {
                    crate::rust_ir::Literal::String(text.trim_matches('"').to_string())
                } else if text == "true" {
                    crate::rust_ir::Literal::Bool(true)
                } else if text == "false" {
                    crate::rust_ir::Literal::Bool(false)
                } else if let Ok(int_val) = text.parse::<i64>() {
                    crate::rust_ir::Literal::Int(int_val)
                } else if let Ok(float_val) = text.parse::<f64>() {
                    crate::rust_ir::Literal::Float(float_val)
                } else if text.starts_with('\'') && text.len() == 3 {
                    crate::rust_ir::Literal::Char(text.chars().nth(1).unwrap_or('?'))
                } else {
                    crate::rust_ir::Literal::Unit
                };
                crate::rust_ir::Pattern::Literal(ir_lit)
            } else {
                crate::rust_ir::Pattern::Wildcard
            }
        }
        // For more complex patterns, use simplified conversion for now
        _ => crate::rust_ir::Pattern::Wildcard,
    }
}

/// Convert AST block to RustIR Block (works with syntax AST)
fn convert_ast_block_to_ir(
    sema: &Semantics<RootDatabase>,
    ast_block: &ra_ap_syntax::ast::BlockExpr,
) -> crate::rust_ir::Block {
    let mut statements = Vec::new();
    let mut tail = None;

    // Process statements in the block
    for stmt in ast_block.statements() {
        match stmt {
            ra_ap_syntax::ast::Stmt::LetStmt(let_stmt) => {
                // Convert let statements with FULL TYPE INFORMATION
                let name = if let Some(pat) = let_stmt.pat() {
                    match pat {
                        ra_ap_syntax::ast::Pat::IdentPat(ident) => ident
                            .name()
                            .map(|n| n.to_string())
                            .unwrap_or_else(|| "unnamed".to_string()),
                        _ => format!("{:?}", pat), // Simplified pattern handling
                    }
                } else {
                    "unnamed".to_string()
                };

                // HYBRID APPROACH: Get inferred type information from Semantics
                let ty = if let Some(pat) = let_stmt.pat() {
                    // Get the inferred type even if not explicitly written!
                    sema.type_of_pat(&pat)
                        .map(|type_info| convert_type(sema, &type_info.original))
                } else if let Some(_ty_annotation) = let_stmt.ty() {
                    // Fallback: convert explicit type annotation
                    // TODO: Convert TypeRef to our Type system
                    Some(crate::rust_ir::Type::Path(vec!["explicit".to_string()]))
                } else {
                    None
                };

                let init = let_stmt
                    .initializer()
                    .map(|expr| convert_ast_expr_with_types(sema, &expr));

                statements.push(crate::rust_ir::Stmt::Let { name, ty, init });
            }
            ra_ap_syntax::ast::Stmt::ExprStmt(expr_stmt) => {
                if let Some(expr) = expr_stmt.expr() {
                    let ir_expr = convert_ast_expr_with_types(sema, &expr);
                    statements.push(crate::rust_ir::Stmt::Expr(ir_expr));
                } else {
                    eprintln!("DEBUG: skipping expr_stmt with no expr");
                }
            }
            ra_ap_syntax::ast::Stmt::Item(item) => {
                // Handle item statements (nested functions, etc.)
                // For now, skip these
                eprintln!("DEBUG: skipping item statement {:?}", item);
            }
        }
    }

    // Handle tail expression
    if let Some(tail_expr) = ast_block.tail_expr() {
        tail = Some(Box::new(convert_ast_expr_with_types(sema, &tail_expr)));
    }

    crate::rust_ir::Block { statements, tail }
}

/// Convert AST expressions to RustIR expressions with full type information
/// This is our main expression conversion function using the AST + Semantics hybrid approach
fn convert_ast_expr_with_types(
    sema: &Semantics<RootDatabase>,
    ast_expr: &ra_ap_syntax::ast::Expr,
) -> crate::rust_ir::Expr {
    use crate::rust_ir::{Expr as IrExpr, Literal as IrLit};
    use ra_ap_syntax::ast::Expr;

    // HYBRID APPROACH: Get type information from Semantics for this expression
    let _expr_type = sema.type_of_expr(ast_expr).map(|type_info| {
        // Convert HIR type to our IR type system
        convert_type(sema, &type_info.original)
    });

    // TODO: We could store this type information in our IR expressions
    // For now, we use it for type-aware conversion logic

    match ast_expr {
        Expr::Literal(lit) => {
            // Convert literal values with type awareness
            let text = lit.token().text().to_string();
            let ir_lit = if text.starts_with('"') {
                IrLit::String(text.trim_matches('"').to_string())
            } else if text == "true" {
                IrLit::Bool(true)
            } else if text == "false" {
                IrLit::Bool(false)
            } else if let Ok(int_val) = text.parse::<i64>() {
                IrLit::Int(int_val)
            } else if let Ok(float_val) = text.parse::<f64>() {
                IrLit::Float(float_val)
            } else if text.starts_with('\'') && text.len() == 3 {
                IrLit::Char(text.chars().nth(1).unwrap_or('?'))
            } else {
                IrLit::Unit
            };
            IrExpr::Literal(ir_lit)
        }
        Expr::PathExpr(path_expr) => {
            // Convert path expressions like variable references
            if let Some(path) = path_expr.path() {
                let segments = path
                    .segments()
                    .filter_map(|seg| seg.name_ref().map(|n| n.to_string()))
                    .collect();
                IrExpr::Path(segments)
            } else {
                IrExpr::Path(vec!["unknown".to_string()])
            }
        }
        Expr::CallExpr(call_expr) => {
            let callee = if let Some(expr) = call_expr.expr() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Path(vec!["unknown".to_string()]))
            };

            let args = if let Some(arg_list) = call_expr.arg_list() {
                arg_list
                    .args()
                    .map(|arg| convert_ast_expr_with_types(sema, &arg))
                    .collect()
            } else {
                Vec::new()
            };

            IrExpr::Call { callee, args }
        }
        Expr::MethodCallExpr(method_call) => {
            let receiver = if let Some(expr) = method_call.receiver() {
                Box::new(convert_ast_expr_with_types(sema, &expr))
            } else {
                Box::new(IrExpr::Path(vec!["unknown".to_string()]))
            };

            let method = method_call
                .name_ref()
                .map(|n| n.to_string())
                .unwrap_or_else(|| "unknown".to_string());

            let args = if let Some(arg_list) = method_call.arg_list() {
                arg_list
                    .args()
                    .map(|arg| convert_ast_expr_with_types(sema, &arg))
                    .collect()
            } else {
                Vec::new()
            };

            IrExpr::MethodCall {
                receiver,
                method,
                args,
            }
        }
        Expr::BinExpr(bin_expr) => convert_bin_expr(sema, bin_expr),
        Expr::IfExpr(if_expr) => {
            let cond = if let Some(condition) = if_expr.condition() {
                Box::new(convert_ast_expr_with_types(sema, &condition))
            } else {
                Box::new(IrExpr::Literal(IrLit::Bool(true)))
            };

            let then_branch = if let Some(then_block) = if_expr.then_branch() {
                Box::new(convert_ast_block_to_ir(sema, &then_block))
            } else {
                Box::new(crate::rust_ir::Block {
                    statements: Vec::new(),
                    tail: None,
                })
            };

            let else_branch = if_expr.else_branch().map(|else_branch| {
                // Handle different else branch types (ElseBranch can be IfExpr or BlockExpr)
                match else_branch {
                    ra_ap_syntax::ast::ElseBranch::Block(block) => {
                        Box::new(convert_ast_block_to_ir(sema, &block))
                    }
                    ra_ap_syntax::ast::ElseBranch::IfExpr(else_if) => {
                        // Convert else if to a block containing the if expression
                        Box::new(crate::rust_ir::Block {
                            statements: Vec::new(),
                            tail: Some(Box::new(convert_ast_expr_with_types(
                                sema,
                                &ra_ap_syntax::ast::Expr::IfExpr(else_if),
                            ))),
                        })
                    }
                }
            });

            IrExpr::If {
                cond,
                then_branch,
                else_branch,
            }
        }
        Expr::WhileExpr(while_expr) => {
            let cond = if let Some(condition) = while_expr.condition() {
                Box::new(convert_ast_expr_with_types(sema, &condition))
            } else {
                Box::new(IrExpr::Literal(IrLit::Bool(true)))
            };

            use ra_ap_syntax::ast::HasLoopBody;
            let body = if let Some(while_block) = while_expr.loop_body() {
                Box::new(convert_ast_block_to_ir(sema, &while_block))
            } else {
                Box::new(crate::rust_ir::Block {
                    statements: Vec::new(),
                    tail: None,
                })
            };

            IrExpr::While { cond, body }
        }
        Expr::ForExpr(for_expr) => {
            let _pat = if let Some(pattern) = for_expr.pat() {
                convert_ast_pattern(sema, &pattern)
            } else {
                crate::rust_ir::Pattern::Wildcard
            };

            let _iter = if let Some(expression) = for_expr.iterable() {
                Box::new(convert_ast_expr_with_types(sema, &expression))
            } else {
                Box::new(IrExpr::Literal(IrLit::Unit))
            };

            use ra_ap_syntax::ast::HasLoopBody;
            let _body = if let Some(for_block) = for_expr.loop_body() {
                Box::new(convert_ast_block_to_ir(sema, &for_block))
            } else {
                Box::new(crate::rust_ir::Block {
                    statements: Vec::new(),
                    tail: None,
                })
            };

            unimplemented!("for loop not implemented");
            // IrExpr::For { pat, iter, body }
        }
        Expr::BlockExpr(block_expr) => {
            IrExpr::BlockExpr(Box::new(convert_ast_block_to_ir(sema, block_expr)))
        }
        Expr::PrefixExpr(prefix_expr) => {
            let op = match prefix_expr.op_kind() {
                Some(ra_ap_syntax::ast::UnaryOp::Neg) => rust_ir::UnOp::Neg,
                Some(ra_ap_syntax::ast::UnaryOp::Not) => rust_ir::UnOp::Not,
                Some(ra_ap_syntax::ast::UnaryOp::Deref) => rust_ir::UnOp::Deref,
                None => panic!("No op kind for prefix expression"),
            };
            let expr = if let Some(inner_expr) = prefix_expr.expr() {
                Box::new(convert_ast_expr_with_types(sema, &inner_expr))
            } else {
                panic!("No inner expression for prefix operation");
            };
            IrExpr::Unary { op, expr }
        },
        Expr::IndexExpr(idx_expr) => {
            // TODO: Consider adding a primitive for primitive array indexing.
            //       This would allow us to build a generic set of axioms for
            //       handling pointer reasoning.
            let base_ast  = idx_expr.base().expect("index without base");
            let index_ast = idx_expr.index().expect("index without index");

            // Types from Semantics
            let self_ty = sema.type_of_expr(&base_ast)
                .map(|t| convert_type(sema, &t.adjusted()))
                .unwrap_or(ir::Type::Path(vec!["_".into()]));
            let idx_ty = sema.type_of_expr(&index_ast)
                .map(|t| convert_type(sema, &t.adjusted()))
                .unwrap_or(ir::Type::Path(vec!["_".into()]));

            let base_ir  = convert_ast_expr_with_types(sema, &base_ast);
            let index_ir = convert_ast_expr_with_types(sema, &index_ast);

            let ufcs = ir::Expr::TraitUfcsCallExpr(ir::TraitUfcsCall {
                trait_path: vec!["core".into(), "ops".into(), "Index".into()],
                self_ty,
                trait_args: vec![idx_ty],
                method: "index".into(),
                args: vec![
                    ir::Expr::AddressOf { mutable: false, expr: Box::new(base_ir) },
                    index_ir,
                ],
            });

            ir::Expr::Unary { op: ir::UnOp::Deref, expr: Box::new(ufcs) }
        },
        Expr::MacroExpr(_) => {
            eprintln!("Macro expression not supported in verification attributes");
            IrExpr::Literal(IrLit::Unit)
        },
        _ => {
            unimplemented!("unsupported expr kind: {:?}", ast_expr);
        },
    }
}

fn convert_bin_expr(
    sema: &Semantics<RootDatabase>,
    bin_expr: &ra_ap_syntax::ast::BinExpr,
) -> crate::rust_ir::Expr {
    use crate::rust_ir::{BinOp, Expr as IrExpr, Literal as IrLit};

    let lhs = if let Some(expr) = bin_expr.lhs() {
        Box::new(convert_ast_expr_with_types(sema, &expr))
    } else {
        Box::new(IrExpr::Literal(IrLit::Unit))
    };

    let rhs = if let Some(expr) = bin_expr.rhs() {
        Box::new(convert_ast_expr_with_types(sema, &expr))
    } else {
        Box::new(IrExpr::Literal(IrLit::Unit))
    };

    use ra_ap_syntax::ast::{BinaryOp, ArithOp, CmpOp, LogicOp};

    let op = if let Some(op_token) = bin_expr.op_kind() {
        match op_token {
            BinaryOp::ArithOp(arith_op) => match arith_op {
                ArithOp::Add => BinOp::Add,
                ArithOp::Sub => BinOp::Sub,
                ArithOp::Mul => BinOp::Mul,
                ArithOp::Div => BinOp::Div,
                ArithOp::Rem => BinOp::Rem,
                _ => unimplemented!("unsupported arithmetic operation: {:?}", arith_op),
            },
            BinaryOp::CmpOp(cmp_op) => match cmp_op {
                CmpOp::Eq { negated: false } => BinOp::Eq,
                CmpOp::Eq { negated: true } => BinOp::NotEq,
                CmpOp::Ord {
                    ordering: ord,
                    strict: is_strict,
                } => match (ord, is_strict) {
                    (ra_ap_syntax::ast::Ordering::Less, false) => BinOp::Lt,
                    (ra_ap_syntax::ast::Ordering::Less, true) => BinOp::Le,
                    (ra_ap_syntax::ast::Ordering::Greater, false) => BinOp::Gt,
                    (ra_ap_syntax::ast::Ordering::Greater, true) => BinOp::Ge,
                },
            },
            BinaryOp::LogicOp(logic_op) => match logic_op {
                LogicOp::And => BinOp::And,
                LogicOp::Or => BinOp::Or,
            },
            BinaryOp::Assignment { op: None } => BinOp::Assign,
            BinaryOp::Assignment { op: Some(arith_op) } => {
                convert_assign_arith_expr(sema, arith_op, lhs.as_ref(), rhs.as_ref())
            },
        }
    } else {
        BinOp::Add
    };

    IrExpr::Binary { op, lhs, rhs }
}

/// TODO: Use the UFCS call to represent this.
/// Some design decisions here:
/// * Consider desugaring primitive type expressions to assignment to the
///    arithmetic operators. For example, `j += 1` ~~> `j = j + 1` if `j` is an
///    `i32`.
/// * For other types, desugar to the UFCS (Universal Function Call Syntax). So
///   for a user-defined type `T` being AddAssigned to u: U,
///   `x += 1` ~~> `<T as core::ops::AddAssign<U>>::add_assign(&mut x, u)`.
fn convert_assign_arith_expr(
    _sema: &Semantics<RootDatabase>,
    _arith_op: ra_ap_syntax::ast::ArithOp,
    _lhs: &rust_ir::Expr,
    _rhs: &rust_ir::Expr,
) -> crate::rust_ir::BinOp {
    unimplemented!("assignment arithmetic not implemented");
}

// ============================================================================
// Syntax-only fallback converters (no type information)
// ============================================================================

/// Fallback function converter when HIR resolution fails
fn convert_function_syntax_fallback(fn_def: &ast::Fn) -> IrFunction {
    let name = fn_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "unnamed".to_string());

    // Extract parameters without type information
    let params = fn_def
        .param_list()
        .map(|list| {
            list.params()
                .map(|param| {
                    let name = param
                        .pat()
                        .and_then(|pat| match pat {
                            ast::Pat::IdentPat(ident) => ident.name().map(|n| n.to_string()),
                            _ => None,
                        })
                        .unwrap_or_else(|| "param".to_string());

                    // Without HIR, we can only get syntax-level type info
                    let ty = param
                        .ty()
                        .map(|type_ref| convert_type_ref_syntax(&type_ref))
                        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

                    (name, ty)
                })
                .collect()
        })
        .unwrap_or_else(Vec::new);

    // Extract return type without semantic analysis
    let return_type = fn_def
        .ret_type()
        .and_then(|ret| ret.ty())
        .map(|type_ref| convert_type_ref_syntax(&type_ref));

    // Extract generics (syntax-only)
    let generics = fn_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    // Create empty body for now (syntax parsing would be complex)
    let body = crate::rust_ir::Block {
        statements: Vec::new(),
        tail: None,
    };

    IrFunction {
        name,
        generics,
        params,
        return_type,
        spec: FunctionSpec {
            pre: None,
            post: None,
        },
        body,
    }
}

/// Convert syntax TypeRef to our Type system (no semantic info)
fn convert_type_ref_syntax(type_ref: &ast::Type) -> crate::rust_ir::Type {
    // This is a simplified conversion that only looks at syntax
    use crate::rust_ir::Type;

    match type_ref {
        ast::Type::PathType(path_type) => {
            if let Some(path) = path_type.path() {
                let segments = path
                    .segments()
                    .map(|seg| {
                        seg.name_ref()
                            .map(|n| n.to_string())
                            .unwrap_or_else(|| "unknown".to_string())
                    })
                    .collect();
                Type::Path(segments)
            } else {
                Type::Path(vec!["unknown".to_string()])
            }
        }
        ast::Type::RefType(ref_type) => {
            let mutable = ref_type.mut_token().is_some();
            let inner = ref_type
                .ty()
                .map(|inner_ty| Box::new(convert_type_ref_syntax(&inner_ty)))
                .unwrap_or_else(|| Box::new(Type::Path(vec!["unknown".to_string()])));
            Type::Reference { mutable, inner }
        }
        _ => {
            // Fallback for complex types we don't handle yet
            Type::Path(vec!["unknown".to_string()])
        }
    }
}

/// Convert syntax generic parameter to our GenericParam (no bounds info)
fn convert_generic_param_syntax(param: &ast::GenericParam) -> crate::rust_ir::GenericParam {
    let name = match param {
        ast::GenericParam::TypeParam(type_param) => type_param
            .name()
            .map(|n| n.to_string())
            .unwrap_or_else(|| "T".to_string()),
        ast::GenericParam::LifetimeParam(lifetime_param) => lifetime_param
            .lifetime()
            .map(|lt| lt.to_string())
            .unwrap_or_else(|| "'a".to_string()),
        ast::GenericParam::ConstParam(const_param) => const_param
            .name()
            .map(|n| n.to_string())
            .unwrap_or_else(|| "N".to_string()),
    };

    crate::rust_ir::GenericParam {
        name,
        bounds: Vec::new(), // Can't resolve bounds without semantics
    }
}

/// Syntax-only fallback converters for other item types
fn convert_struct_syntax_fallback(struct_def: &ast::Struct) -> crate::rust_ir::StructDef {
    let name = struct_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedStruct".to_string());

    let generics = struct_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let fields = struct_def
        .field_list()
        .map(|field_list| match field_list {
            ast::FieldList::RecordFieldList(record_fields) => record_fields
                .fields()
                .map(|field| crate::rust_ir::StructField {
                    name: field
                        .name()
                        .map(|n| n.to_string())
                        .unwrap_or_else(|| "field".to_string()),
                    ty: field
                        .ty()
                        .map(|ty| convert_type_ref_syntax(&ty))
                        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()])),
                })
                .collect(),
            _ => Vec::new(), // Tuple structs would need different handling
        })
        .unwrap_or_else(Vec::new);

    crate::rust_ir::StructDef {
        name,
        generics,
        fields,
    }
}

fn convert_enum_syntax_fallback(enum_def: &ast::Enum) -> crate::rust_ir::EnumDef {
    let name = enum_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedEnum".to_string());

    let generics = enum_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let variants = enum_def
        .variant_list()
        .map(|variant_list| {
            variant_list
                .variants()
                .map(|variant| crate::rust_ir::VariantDef {
                    name: variant
                        .name()
                        .map(|n| n.to_string())
                        .unwrap_or_else(|| "Variant".to_string()),
                    payload: crate::rust_ir::VariantPayload::Unit, // Simplified - would need more work for full support
                })
                .collect()
        })
        .unwrap_or_else(Vec::new);

    crate::rust_ir::EnumDef {
        name,
        generics,
        variants,
    }
}

fn convert_const_syntax_fallback(const_def: &ast::Const) -> crate::rust_ir::ConstantDef {
    let name = const_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UNNAMED".to_string());

    let ty = const_def
        .ty()
        .map(|type_ref| convert_type_ref_syntax(&type_ref))
        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

    // Without semantics, we can't evaluate the expression properly
    let expr = crate::rust_ir::Expr::Literal(crate::rust_ir::Literal::Unit);

    crate::rust_ir::ConstantDef { name, ty, expr }
}

fn convert_type_alias_syntax_fallback(
    type_alias_def: &ast::TypeAlias,
) -> crate::rust_ir::TypeAliasDef {
    let name = type_alias_def
        .name()
        .map(|n| n.to_string())
        .unwrap_or_else(|| "UnnamedType".to_string());

    let generics = type_alias_def
        .generic_param_list()
        .map(|list| {
            list.generic_params()
                .map(|param| convert_generic_param_syntax(&param))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let aliased = type_alias_def
        .ty()
        .map(|type_ref| convert_type_ref_syntax(&type_ref))
        .unwrap_or_else(|| crate::rust_ir::Type::Path(vec!["unknown".to_string()]));

    crate::rust_ir::TypeAliasDef {
        name,
        generics,
        aliased,
    }
}

fn convert_attr_expr_syntax_only(ast_expr: &ast::Expr) -> ir::Expr {
    use ast::Expr;
    use ir::{BinOp as IrBin, Expr as IrExpr, Literal as IrLit, UnOp as IrUn};
    use ra_ap_syntax::{ast, AstNode};

    match ast_expr {
        // Indexing: e[i]
        Expr::IndexExpr(idx) => {
            let base = idx
                .base()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
            let index = idx
                .index()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Literal(IrLit::Int(0)));
            IrExpr::Index {
                target: Box::new(base),
                index: Box::new(index),
            }
        }
        Expr::FieldExpr(fe) => {
            let target = fe
                .expr()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
            let field = fe
                .name_ref()
                .map(|n| n.text().to_string())
                .unwrap_or_else(|| "<field>".into());
            IrExpr::FieldAccess {
                target: Box::new(target),
                field,
            }
        }

        // Match
        // Expr::MatchExpr(me) => {
        //     let scrutinee = me.expr().as_ref().map(convert_attr_expr_syntax_only)
        //         .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
        //     let arms = me.arms().map(|arm| {
        //         let pat = arm.pat()
        //             .map(|p| convert_attr_pattern_syntax_only(&p))
        //             .unwrap_or(ir::Pattern::Wildcard);
        //         if let Some(block) = arm.block() {
        //             (pat, convert_attr_block_syntax_only(&block))
        //         } else if let Some(expr) = arm.expr() {
        //             (pat, ir::Block { statements: vec![], tail: Some(Box::new(convert_attr_expr_syntax_only(&expr))) })
        //         } else {
        //             (pat, ir::Block { statements: vec![], tail: None })
        //         }
        //     }).collect();
        //     IrExpr::Match { expr: Box::new(scrutinee), arms }
        // }

        // Calls, methods, field access
        Expr::CallExpr(call) => {
            let callee = call
                .expr()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
            let args = call
                .arg_list()
                .map(|al| {
                    al.args()
                        .map(|e| convert_attr_expr_syntax_only(&e))
                        .collect()
                })
                .unwrap_or_else(Vec::new);
            IrExpr::Call {
                callee: Box::new(callee),
                args,
            }
        }
        Expr::MethodCallExpr(m) => {
            let recv = m
                .receiver()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
            let name = m
                .name_ref()
                .map(|n| n.text().to_string())
                .unwrap_or_else(|| "<meth>".into());
            let args = m
                .arg_list()
                .map(|al| {
                    al.args()
                        .map(|e| convert_attr_expr_syntax_only(&e))
                        .collect()
                })
                .unwrap_or_else(Vec::new);
            IrExpr::MethodCall {
                receiver: Box::new(recv),
                method: name,
                args,
            }
        }
        // Cast (syntax-only: parse the type token path)
        Expr::CastExpr(ce) => {
            let inner = ce
                .expr()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Path(vec!["_unk".into()]));
            let ty = ce
                .ty()
                .map(|tr| {
                    ir::Type::Path(
                        tr.syntax()
                            .text()
                            .to_string()
                            .split("::")
                            .map(|s| s.trim().to_string())
                            .collect(),
                    )
                })
                .unwrap_or(ir::Type::Path(vec!["_".into()]));
            IrExpr::Cast {
                expr: Box::new(inner),
                ty,
            }
        }

        // Grouping / block
        Expr::ParenExpr(pe) => pe
            .expr()
            .as_ref()
            .map(convert_attr_expr_syntax_only)
            .unwrap_or(IrExpr::Literal(IrLit::Unit)),
        Expr::BlockExpr(be) => IrExpr::BlockExpr(Box::new(convert_attr_block_syntax_only(&be))),

        // Unary/binary (syntax-only op mapping)
        Expr::PrefixExpr(px) => {
            let op = match px.op_kind() {
                Some(ast::UnaryOp::Neg) => IrUn::Neg,
                Some(ast::UnaryOp::Not) => IrUn::Not,
                Some(ast::UnaryOp::Deref) => IrUn::Deref,
                None => panic!("not sure to do with a prefix operation with no op kind"),
            };
            let inner = px
                .expr()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Literal(IrLit::Unit));
            IrExpr::Unary {
                op,
                expr: Box::new(inner),
            }
        }
        Expr::BinExpr(bx) => {
            let lhs = bx
                .lhs()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Literal(IrLit::Unit));
            let rhs = bx
                .rhs()
                .as_ref()
                .map(convert_attr_expr_syntax_only)
                .unwrap_or(IrExpr::Literal(IrLit::Unit));
            let op = match bx.op_kind() {
                Some(ra_ap_syntax::ast::BinaryOp::ArithOp(arith_op)) => match arith_op {
                    ra_ap_syntax::ast::ArithOp::Add => IrBin::Add,
                    ra_ap_syntax::ast::ArithOp::Sub => IrBin::Sub,
                    ra_ap_syntax::ast::ArithOp::Mul => IrBin::Mul,
                    ra_ap_syntax::ast::ArithOp::Div => IrBin::Div,
                    ra_ap_syntax::ast::ArithOp::Rem => IrBin::Rem,
                    _ => IrBin::Add,
                },
                Some(ra_ap_syntax::ast::BinaryOp::CmpOp(cmp_op)) => match cmp_op {
                    ra_ap_syntax::ast::CmpOp::Eq { .. } => IrBin::Eq,
                    ra_ap_syntax::ast::CmpOp::Ord { .. } => IrBin::Lt, // Simplified - would need to extract actual ordering
                },
                Some(ra_ap_syntax::ast::BinaryOp::LogicOp(logic_op)) => match logic_op {
                    ra_ap_syntax::ast::LogicOp::And => IrBin::And,
                    ra_ap_syntax::ast::LogicOp::Or => IrBin::Or,
                },
                // FIXME: Need to consider adding support for these.
                Some(ra_ap_syntax::ast::BinaryOp::Assignment { .. }) => {
                    panic!("Arithmetic assignment not supported in attributes")
                }
                None => panic!("No op kind for binary expression"),
            };
            IrExpr::Binary {
                op,
                lhs: Box::new(lhs),
                rhs: Box::new(rhs),
            }
        }

        // Paths & literals
        Expr::PathExpr(px) => {
            let segs = px
                .path()
                .map(|p| {
                    p.segments()
                        .filter_map(|s| s.name_ref().map(|n| n.text().to_string()))
                        .collect()
                })
                .unwrap_or_else(|| vec!["_unk".into()]);
            IrExpr::Path(segs)
        }
        Expr::Literal(l) => {
            let txt = l.syntax().text().to_string();
            let lit = if txt == "true" || txt == "false" {
                ir::Literal::Bool(txt == "true")
            } else if txt.starts_with('"') && txt.ends_with('"') {
                ir::Literal::String(txt.trim_matches('"').to_string())
            } else if txt == "()" {
                ir::Literal::Unit
            } else if txt.contains('.') {
                txt.parse::<f64>()
                    .map(ir::Literal::Float)
                    .unwrap_or(ir::Literal::Unit)
            } else {
                txt.parse::<i64>()
                    .map(ir::Literal::Int)
                    .unwrap_or(ir::Literal::Unit)
            };
            IrExpr::Literal(lit)
        }

        _ => ir::Expr::Path(vec!["<unsupported-attr-expr>".into()]),
    }
}

fn convert_attr_block_syntax_only(block: &ast::BlockExpr) -> ir::Block {
    let mut statements = Vec::new();
    for stmt in block.statements() {
        match stmt {
            ast::Stmt::LetStmt(let_stmt) => {
                // syntax-only: no type inference
                let name = let_stmt
                    .pat()
                    .and_then(|p| match p {
                        ast::Pat::IdentPat(id) => id.name().map(|n| n.text().to_string()),
                        _ => None,
                    })
                    .unwrap_or_else(|| "_".into());
                let init = let_stmt
                    .initializer()
                    .map(|e| convert_attr_expr_syntax_only(&e));
                statements.push(ir::Stmt::Let {
                    name,
                    ty: None,
                    init,
                });
            }
            ast::Stmt::ExprStmt(expr_stmt) => {
                if let Some(e) = expr_stmt.expr() {
                    statements.push(ir::Stmt::Expr(convert_attr_expr_syntax_only(&e)));
                }
            }
            _ => {}
        }
    }
    let tail = block
        .tail_expr()
        .map(|e| Box::new(convert_attr_expr_syntax_only(&e)));
    ir::Block { statements, tail }
}

fn convert_attr_pattern_syntax_only(pat: &ast::Pat) -> ir::Pattern {
    use ir::Pattern as IrPat;
    match pat {
        ast::Pat::WildcardPat(_) => IrPat::Wildcard,
        ast::Pat::IdentPat(id) => id
            .name()
            .map(|n| IrPat::Ident(n.text().to_string()))
            .unwrap_or(IrPat::Wildcard),
        ast::Pat::LiteralPat(l) => {
            let txt = l.syntax().text().to_string();
            let lit = if txt == "true" || txt == "false" {
                ir::Literal::Bool(txt == "true")
            } else if txt == "()" {
                ir::Literal::Unit
            } else if txt.contains('.') {
                txt.parse::<f64>()
                    .map(ir::Literal::Float)
                    .unwrap_or(ir::Literal::Unit)
            } else {
                txt.parse::<i64>()
                    .map(ir::Literal::Int)
                    .unwrap_or(ir::Literal::Unit)
            };
            IrPat::Literal(lit)
        }
        ast::Pat::TuplePat(tp) => IrPat::Tuple(
            tp.fields()
                .map(|p| convert_attr_pattern_syntax_only(&p))
                .collect(),
        ),
        ast::Pat::RecordPat(rp) => {
            let path = rp
                .path()
                .map(|p| {
                    p.segments()
                        .filter_map(|s| s.name_ref().map(|n| n.text().to_string()))
                        .collect()
                })
                .unwrap_or_default();
            let fields = rp
                .record_pat_field_list()
                .map(|fl| {
                    fl.fields()
                        .filter_map(|f| {
                            let name = f.name_ref()?.text().to_string();
                            let sub = f
                                .pat()
                                .map(|pp| convert_attr_pattern_syntax_only(&pp))
                                .unwrap_or(IrPat::Wildcard);
                            Some((name, sub))
                        })
                        .collect()
                })
                .unwrap_or_default();
            IrPat::Struct { path, fields }
        }
        ast::Pat::TupleStructPat(ts) => {
            let path = ts
                .path()
                .map(|p| {
                    p.segments()
                        .filter_map(|s| s.name_ref().map(|n| n.text().to_string()))
                        .collect()
                })
                .unwrap_or_default();
            let elems = ts
                .fields()
                .map(|p| convert_attr_pattern_syntax_only(&p))
                .collect();
            IrPat::TupleStruct { path, elems }
        }
        _ => IrPat::Wildcard,
    }
}

// fn parse_attr_expr_to_ir(attr: &ast::Attr) -> Option<ir::Expr> {
//     let raw = if let Some(tt) = attr.token_tree() {
//         tt.syntax().text().to_string()
//     } else {
//         attr.syntax().text().to_string()
//     };
//     let text = raw.trim().trim_start_matches('=').trim();
//     if text.is_empty() { return None; }

//     // Wrap for parsing and extract the inner expression syntactically
//     let wrapper = format!("fn __attr_expr__() {{ let _ = {{ {} }}; }}", text);
//     let file = SourceFile::parse(&wrapper, Edition::CURRENT).tree();

//     for item in file.items() {
//         if let ast::Item::Fn(f) = item {
//             if let Some(body) = f.body() {
//                 for stmt in body.statements() {
//                     if let ast::Stmt::LetStmt(let_stmt) = stmt {
//                         if let Some(init) = let_stmt.initializer() {
//                             return Some(convert_attr_expr_syntax_only(&init));
//                         }
//                     }
//                 }
//                 if let Some(tail) = body.tail_expr() {
//                     return Some(convert_attr_expr_syntax_only(&tail));
//                 }
//             }
//         }
//     }
//     None
// }

fn extract_fn_spec_from_attrs(ast_fn: &ast::Fn) -> ir::FunctionSpec {
    let mut pre: Option<ir::Expr> = None;
    let mut post: Option<ir::Expr> = None;
    for attr in ast_fn.attrs() {
        if let Some(path) = attr.path() {
            let name = path.syntax().text().to_string();
            if name == "pre" {
                pre = parse_attr_expr_to_ir(&attr);
            }
            if name == "post" {
                post = parse_attr_expr_to_ir(&attr);
            }
        }
    }
    ir::FunctionSpec { pre, post }
}

fn extract_quoted_substring(text: &str) -> Option<String> {
    if let (Some(start), Some(end)) = (text.find('"'), text.rfind('"')) {
        if start < end {
            let inner = &text[start + 1..end]; // skip the first quote, stop before the last
            return Some(inner.to_string());
        } else {
            None
        }
    } else {
        None
    }
}

fn parse_attr_expr_to_ir(attr: &ast::Attr) -> Option<ir::Expr> {
    let raw = if let Some(tt) = attr.token_tree() {
        tt.syntax().text().to_string()
    } else {
        attr.syntax().text().to_string()
    };

    // The condition is wrapped in double quotes. So we trim all chacters up to
    // the first `"` and trim all characters after hte last '"'.
    let Some(text) = extract_quoted_substring(raw.as_str()) else {
        return None;
    };
    if text.is_empty() {
        return None;
    }

    // Parse as a tiny function wrapper so we can grab the inner expr.
    let wrapper = format!("fn __attr_expr__() {{ let _ = {{ {} }}; }}", text);
    let file = SourceFile::parse(&wrapper, Edition::CURRENT).tree();

    for item in file.items() {
        if let ast::Item::Fn(f) = item {
            if let Some(body) = f.body() {
                for stmt in body.statements() {
                    if let ast::Stmt::LetStmt(let_stmt) = stmt {
                        if let Some(init) = let_stmt.initializer() {
                            let ir = convert_attr_expr_syntax_only(&init);
                            return Some(unwrap_inline_block(ir));
                        }
                    }
                }
                if let Some(tail) = body.tail_expr() {
                    let ir = convert_attr_expr_syntax_only(&tail);
                    return Some(unwrap_inline_block(ir));
                }
            }
        }
    }
    None
}

/// If `expr` is `{ <e> }` (no statements, only a tail), return `<e>`.
fn unwrap_inline_block(expr: ir::Expr) -> ir::Expr {
    match expr {
        ir::Expr::BlockExpr(boxed) if boxed.statements.is_empty() => {
            if let Some(e) = boxed.tail {
                *e
            } else {
                ir::Expr::BlockExpr(Box::new(ir::Block {
                    statements: boxed.statements,
                    tail: None,
                }))
            }
        }
        other => other,
    }
}
