use clap::ValueEnum;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, ValueEnum)]
pub enum Stage {
    /// Parse & type-check rust; build RustIR
    Translate,
    /// Run WP & collect verification conditions (VCs)
    Vcs,
    /// (future) Emit Lean project with IR + VCs
    Complete,
}

impl Stage {
    pub fn includes(self, other: Stage) -> bool {
        use Stage::*;
        match (self, other) {
            (Complete, _) => true,
            (Vcs, Translate) => true,
            (a, b) => a == b,
        }
    }
}
