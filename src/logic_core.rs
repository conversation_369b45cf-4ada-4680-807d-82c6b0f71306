// ========================= src/logic_core.rs =========================
#![allow(dead_code)]

use std::fmt::{self, Display, Formatter};

// ---- Types used only for quantifier binders / pretty-printing ----
#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum Ty {
    Int,
    Bool,
    Unit,
    Tuple(Vec<Ty>),
    User(String),
}

impl Display for Ty {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Ty::Int => write!(f, "Int"),
            Ty::Bool => write!(f, "bool"),
            Ty::Unit => write!(f, "()"),
            Ty::Tuple(ts) => {
                write!(f, "(")?;
                for (i, t) in ts.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", t)?;
                }
                write!(f, ")")
            }
            Ty::User(s) => write!(f, "{s}"),
        }
    }
}

// ---- Literals/Terms/Formula -------------------------------------------------
#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum Literal {
    Int(i128),
    Bool(bool),
}

impl Display for Literal {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Literal::Int(n) => write!(f, "{n}"),
            Literal::Bool(b) => write!(f, "{b}"),
        }
    }
}

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum Term {
    Var(String),
    Lit(Literal),
    Tuple(Vec<Term>),
    App(&'static str, Vec<Term>), // e.g., "+", "/", "cast", "field", "idx"
}

impl Term {
    pub fn var<S: Into<String>>(s: S) -> Self {
        Term::Var(s.into())
    }
    pub fn lit_i(n: i128) -> Self {
        Term::Lit(Literal::Int(n))
    }
    pub fn lit_b(b: bool) -> Self {
        Term::Lit(Literal::Bool(b))
    }
}

impl Display for Term {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Term::Var(s) => write!(f, "{s}"),
            Term::Lit(l) => write!(f, "{l}"),
            Term::Tuple(ts) => {
                write!(f, "(")?;
                for (i, t) in ts.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{t}")?;
                }
                write!(f, ")")
            }
            Term::App(sym, args) => {
                match *sym {
                    "+" | "-" | "*" | "/" | "%" | "<" | "<=" | ">" | ">=" | "==" | "!=" => {
                        if args.len() == 2 {
                            return write!(f, "({} {} {})", args[0], sym, args[1]);
                        }
                    }
                    _ => {}
                }
                write!(f, "{}(", sym)?;
                for (i, a) in args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{a}")?;
                }
                write!(f, ")")
            }
        }
    }
}

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub enum Formula {
    True,
    False,
    Eq(Term, Term),
    Pred(&'static str, Vec<Term>), // domain preds: neq0, in_bounds, cast_ok, etc.
    Not(Box<Formula>),
    And(Box<Formula>, Box<Formula>),
    Or(Box<Formula>, Box<Formula>),
    Implies(Box<Formula>, Box<Formula>),
    ForAll(Vec<(String, Ty)>, Box<Formula>),
    Exists(Vec<(String, Ty)>, Box<Formula>),
}

impl Formula {
    pub fn tru() -> Self {
        Formula::True
    }
    pub fn fls() -> Self {
        Formula::False
    }
    pub fn eq(a: Term, b: Term) -> Self {
        Formula::Eq(a, b)
    }
    pub fn pred(p: &'static str, args: Vec<Term>) -> Self {
        Formula::Pred(p, args)
    }
    pub fn not(p: Formula) -> Self {
        Formula::Not(Box::new(p))
    }
    pub fn and(p: Formula, q: Formula) -> Self {
        Formula::And(Box::new(p), Box::new(q))
    }
    pub fn or(p: Formula, q: Formula) -> Self {
        Formula::Or(Box::new(p), Box::new(q))
    }
    pub fn imp(p: Formula, q: Formula) -> Self {
        Formula::Implies(Box::new(p), Box::new(q))
    }
    pub fn forall(bvs: Vec<(String, Ty)>, body: Formula) -> Self {
        Formula::ForAll(bvs, Box::new(body))
    }
    pub fn exists(bvs: Vec<(String, Ty)>, body: Formula) -> Self {
        Formula::Exists(bvs, Box::new(body))
    }
}

impl Display for Formula {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        use Formula::*;
        match self {
            True => write!(f, "⊤"),
            False => write!(f, "⊥"),
            Eq(a, b) => write!(f, "({a} = {b})"),
            Pred(p, args) => {
                write!(f, "{}(", p)?;
                for (i, a) in args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{a}")?;
                }
                write!(f, ")")
            }
            Not(p) => write!(f, "¬({})", p),
            And(p, q) => write!(f, "({}) ∧ ({})", p, q),
            Or(p, q) => write!(f, "({}) ∨ ({})", p, q),
            Implies(p, q) => write!(f, "({}) → ({})", p, q),
            ForAll(bvs, body) => {
                write!(f, "∀ ")?;
                for (i, (x, ty)) in bvs.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "({}: {})", x, ty)?;
                }
                write!(f, ". {}", body)
            }
            Exists(bvs, body) => {
                write!(f, "∃ ")?;
                for (i, (x, ty)) in bvs.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "({}: {})", x, ty)?;
                }
                write!(f, ". {}", body)
            }
        }
    }
}

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub struct ValuePost {
    pub v: String,
    pub body: Formula,
}

// ---- Substitution -----------------------------------------------------------

pub fn subst_term(t: &Term, x: &str, by: &Term) -> Term {
    use Term::*;
    match t {
        Var(s) => {
            if s == x {
                by.clone()
            } else {
                Var(s.clone())
            }
        }
        Lit(_) => t.clone(),
        Tuple(ts) => Tuple(ts.iter().map(|e| subst_term(e, x, by)).collect()),
        App(sym, args) => App(sym, args.iter().map(|e| subst_term(e, x, by)).collect()),
    }
}

pub fn subst_formula(phi: &Formula, x: &str, by: &Term) -> Formula {
    use Formula::*;
    match phi {
        True | False => phi.clone(),
        Eq(a, b) => Eq(subst_term(a, x, by), subst_term(b, x, by)),
        Pred(p, args) => Pred(p, args.iter().map(|t| subst_term(t, x, by)).collect()),
        Not(p) => Not(Box::new(subst_formula(p, x, by))),
        And(p, q) => And(
            Box::new(subst_formula(p, x, by)),
            Box::new(subst_formula(q, x, by)),
        ),
        Or(p, q) => Or(
            Box::new(subst_formula(p, x, by)),
            Box::new(subst_formula(q, x, by)),
        ),
        Implies(p, q) => Implies(
            Box::new(subst_formula(p, x, by)),
            Box::new(subst_formula(q, x, by)),
        ),
        ForAll(bvs, body) => {
            if bvs.iter().any(|(v, _)| v == x) {
                phi.clone()
            } else {
                ForAll(bvs.clone(), Box::new(subst_formula(body, x, by)))
            }
        }
        Exists(bvs, body) => {
            if bvs.iter().any(|(v, _)| v == x) {
                phi.clone()
            } else {
                Exists(bvs.clone(), Box::new(subst_formula(body, x, by)))
            }
        }
    }
}

pub fn apply_post(q: &ValuePost, value: Term) -> Formula {
    subst_formula(&q.body, &q.v, &value)
}

// ---- Helpers for common predicates -----------------------------------------

pub fn neq0(t: Term) -> Formula {
    Formula::pred("neq0", vec![t])
}
pub fn in_bounds(arr: Term, idx: Term) -> Formula {
    Formula::pred("in_bounds", vec![arr, idx])
}
pub fn cast_ok(v: Term, ty: Ty) -> Formula {
    Formula::pred(
        "cast_ok",
        vec![v, Term::App("Ty", vec![Term::Var(ty.to_string())])],
    )
}

// ---- Name generator ---------------------------------------------------------
#[derive(Default, Clone)]
pub struct NameGen {
    counter: u64,
}
impl NameGen {
    pub fn fresh<S: AsRef<str>>(&mut self, hint: S) -> String {
        let c = self.counter;
        self.counter += 1;
        format!("{}{}", hint.as_ref(), c)
    }
}

/// Convenience: “value matches pattern” predicate for `match` arms.
pub fn matches_pat(val: Term, pat: Term) -> Formula {
    Formula::Pred("matches", vec![val, pat])
}

/// Convenience: “pattern set is exhaustive for value” (first arg is scrutinee).
pub fn exhaustive(args: Vec<Term>) -> Formula {
    // args[0] = scrutinee; args[1..] = encodings of arm patterns
    Formula::Pred("exhaustive", args)
}
