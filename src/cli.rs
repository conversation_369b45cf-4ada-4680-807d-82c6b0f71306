use clap::Parser;

use crate::stages::Stage;

#[derive(Parser, Debug)]
pub struct Cli {
    /// Path to the Rust project (workspace or crate)
    #[arg(long, value_name = "DIR")] 
    pub rust_project_dir: std::path::PathBuf,

    /// (optional) Dump the RustIR pretty-printed to a file
    #[arg(long, value_name = "FILE")]
    pub dump_rust_ir: Option<std::path::PathBuf>,

    /// Run the pipeline up to (and including) this stage
    #[arg(long, value_enum, default_value_t = Stage::Complete)]
    pub up_to_stage: Stage,

    /// (optional) Dump the VCs pretty-printed to a file
    #[arg(long, value_name = "FILE")]
    pub dump_vcs: Option<std::path::PathBuf>,
}
