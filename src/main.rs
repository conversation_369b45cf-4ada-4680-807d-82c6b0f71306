mod cli;
mod hir_analysis;
mod rust_ir;
mod ra_loader;

mod stages;
mod logic_core;
mod wp;

use anyhow::Result;
use clap::Parser;
use cli::Cli;

fn main() -> Result<()> {
    let args = Cli::parse();

    // Stage: Translate (Rust → RustIR)
    let ir_crate = hir_analysis::analyze_workspace(&args.rust_project_dir)?; // 【5:90†rust-files.md†L103-L107】

    if let Some(path) = args.dump_rust_ir.as_ref() {
        ir_crate.serialize_as_code(path)?; // existing pretty-printer
    }

    // Stage: WP (collect VCs)
    if args.up_to_stage.includes(stages::Stage::Vcs) {
        let sigma = wp::build_sigma_from_crate(&ir_crate);
        let _vc_bundles = wp::generate_vcs_for_crate(&ir_crate, &sigma, &wp::WpOptions { generate_termination_vcs: false });
        // NOTE: no debug/IO yet by request; we just compute and keep in memory.
        // We'll add emission/printing in the next step.
    }

    // Stage: Complete (future) → emit Lean project
    if args.up_to_stage == stages::Stage::Complete {
        // TODO(next): write Lean files for IR + VCs
    }

    Ok(())
}
