//! RustIR AST definitions — a simplified Rust for WP computation
//!
//! RustIR is an intermediate, desugared representation of rust code. In this
//! form, the code is easier to analyze and process using tools like WP the
//! algorithm.

use std::fmt::{self, Display, Formatter};

/// A complete RustIR crate/module
pub struct Crate {
    /// Top-level items in the crate
    pub items: Vec<Item>,
}

impl Crate {
    pub fn new() -> Self {
        Self { items: Vec::new() }
    }

    pub fn append(&mut self, krate: &mut Crate) {
        self.items.append(&mut krate.items);
    }

    /// Serialize the crate as Rust-like code to a file
    pub fn serialize_as_code<P: AsRef<std::path::Path>>(&self, path: P) -> std::io::Result<()> {
        use std::fs::File;
        use std::io::Write;

        let mut file = File::create(path)?;
        write!(file, "{}", self)?;
        Ok(())
    }
}

/// All kinds of top-level definitions
pub enum Item {
    TypeAlias(TypeAliasDef),
    Struct(StructDef),
    Enum(EnumDef),
    Trait(TraitDef),
    Impl(ImplDef),
    Function(FunctionDef),
    Constant(ConstantDef),
}

/// Generic parameter with optional trait bounds
pub struct GenericParam {
    pub name: String,
    pub bounds: Vec<Type>,
}

/// A type alias: `type Name<Params> = Type;`
pub struct TypeAliasDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub aliased: Type,
}

/// A struct definition: `struct Name<Params> { fields }`
pub struct StructDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub fields: Vec<StructField>,
}

pub struct StructField {
    pub name: String,
    pub ty: Type,
}

/// An enum definition: `enum Name<Params> { variants }`
pub struct EnumDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub variants: Vec<VariantDef>,
}

pub struct VariantDef {
    pub name: String,
    pub payload: VariantPayload,
}

pub enum VariantPayload {
    /// No data
    Unit,
    /// Tuple-like `Variant(T1, T2, ...)`
    Tuple(Vec<Type>),
    /// Struct-like `Variant { f1: T1, f2: T2 }`
    Struct(Vec<StructField>),
}

/// A trait definition with supertraits and items
pub struct TraitDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub supertraits: Vec<Type>,
    pub items: Vec<TraitItem>,
}

pub enum TraitItem {
    Method(FunctionSig),
    TypeAlias(TypeAliasDef),
    Const(ConstantSig),
}

/// An impl block, optionally for a trait
pub struct ImplDef {
    /// None = inherent impl; Some(t) = impl of trait t
    pub trait_type: Option<Type>,
    pub self_ty: Type,
    pub generics: Vec<GenericParam>,
    pub items: Vec<ImplItem>,
}

pub enum ImplItem {
    Method(FunctionDef),
    TypeAlias(TypeAliasDef),
    Const(ConstantDef),
}

/// A free-standing function
pub struct FunctionDef {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub params: Vec<(String, Type)>,
    pub return_type: Option<Type>,
    pub spec: FunctionSpec,
    pub body: Block,
}

/// Function specification (optional) with pre/post conditions
pub struct FunctionSpec {
    pub pre: Option<Expr>,
    pub post: Option<Expr>,
}

/// Function signature (no body)
pub struct FunctionSig {
    pub name: String,
    pub generics: Vec<GenericParam>,
    pub params: Vec<(String, Type)>,
    pub return_type: Option<Type>,
}

/// A constant definition with initializer
pub struct ConstantDef {
    pub name: String,
    pub ty: Type,
    pub expr: Expr,
}

/// Constant signature (no expr)
pub struct ConstantSig {
    pub name: String,
    pub ty: Type,
}

/// A block is a sequence of statements ending in an optional expression
pub struct Block {
    pub statements: Vec<Stmt>,
    pub tail: Option<Box<Expr>>,
}

/// Statements in RustIR
pub enum Stmt {
    /// `let x [: Type] [= Expr];`
    Let {
        name: String,
        ty: Option<Type>,
        init: Option<Expr>,
    },
    /// An expression used as a statement
    Expr(Expr),
    /// `assert!(Expr)` or custom assume/assert
    Assert(Expr),
    /// `return [Expr]`
    Return(Option<Expr>),
    /// `break` / `continue`
    Break,
    Continue,
}

/// All expression forms
pub enum Expr {
    /// Literal values
    Literal(Literal),
    /// Variable or path reference, e.g. `x` or `MyType::CONST`
    Path(Vec<String>),
    /// Function or constructor call
    Call { callee: Box<Expr>, args: Vec<Expr> },
    /// Method call `e.method(args)`
    MethodCall {
        receiver: Box<Expr>,
        method: String,
        args: Vec<Expr>,
    },
    /// Field access `e.field`
    FieldAccess { target: Box<Expr>, field: String },
    /// Indexing `e[index]`
    Index { target: Box<Expr>, index: Box<Expr> },
    /// Binary operations: `lhs op rhs`
    Binary {
        op: BinOp,
        lhs: Box<Expr>,
        rhs: Box<Expr>,
    },
    /// Unary operations: `op expr`
    Unary { op: UnOp, expr: Box<Expr> },
    AddressOf{ mutable: bool, expr: Box<Expr>},
    /// `if cond { then } else { else }`
    If {
        cond: Box<Expr>,
        then_branch: Box<Block>,
        else_branch: Option<Box<Block>>,
    },
    /// `while cond { body }`
    While { cond: Box<Expr>, body: Box<Block> },
    /// `match expr { arms }`
    Match {
        expr: Box<Expr>,
        arms: Vec<(Pattern, Box<Block>)>,
    },
    /// Block expression
    BlockExpr(Box<Block>),
    /// Cast `expr as Type`
    Cast { expr: Box<Expr>, ty: Type },
    /// Loop without condition (infinite loop)
    Loop(Box<Block>),
    /// UFCS call to a trait method
    TraitUfcsCallExpr(TraitUfcsCall),
}

pub struct TraitUfcsCall {
    pub trait_path: Vec<String>, // e.g. ["core","ops","Index"]
    pub self_ty: Type,           // concrete Self type
    pub trait_args: Vec<Type>,   // e.g. [IdxTy]
    pub method: String,          // "index" or "index_mut"
    pub args: Vec<Expr>,         // explicit args, first is &self / &mut self
}

/// Patterns for match arms
pub enum Pattern {
    Wildcard,
    Ident(String),
    Literal(Literal),
    Tuple(Vec<Pattern>),
    Struct {
        path: Vec<String>,
        fields: Vec<(String, Pattern)>,
    },
    TupleStruct {
        path: Vec<String>,
        elems: Vec<Pattern>,
    },
}

/// Literal constants
pub enum Literal {
    Int(i64),
    Bool(bool),
    Float(f64),
    Char(char),
    String(String),
    Unit,
}

/// Binary operators
pub enum BinOp {
    Add,
    Sub,
    Mul,
    Div,
    Rem,
    And,
    Or,
    Eq,
    NotEq,
    Lt,
    Le,
    Gt,
    Ge,
    Assign,
}

/// Unary operators
pub enum UnOp {
    Neg,
    Not,
    Deref,
}

pub enum IntPrecision {
    I8,
    I16,
    I32,
    I64,
    I128,
    Isize,
    U8,
    U16,
    U32,
    U64,
    U128,
    Usize,
}

pub enum FloatPrecision {
    F32,
    F64,
    F128,
}

/// Types in RustIR
pub enum Type {
    Never,
    Bool,
    Char,
    Int(IntPrecision),
    Float(FloatPrecision),
    /// Named path, e.g. `std::vec::Vec`
    Path(Vec<String>),
    /// Generic type instantiation, e.g. `Option<T>`
    Generic { base: Box<Type>, args: Vec<Type> },
    /// Type parameter
    TypeParam(String),
    /// Reference `&T` or `&mut T`
    Reference { mutable: bool, inner: Box<Type> },
    /// Array `[T; N]` or slice `[T]`
    Array {
        inner: Box<Type>,
        len: Option<usize>,
    },
    /// Tuple `(T1, T2, ...)`
    Tuple(Vec<Type>),
    /// Function pointer `(T1, T2) -> Tret`
    FnPtr { params: Vec<Type>, ret: Box<Type> },
    /// Associated type abstraction (e.g. `<T as Trait>::Assoc`)
    AssocType { type_: Box<Type>, name: String },
}

// ============================================================================
// Display implementations for debug pretty printing
// ============================================================================

/// Helper struct for indented display
struct IndentedDisplay<'a, T: Display>(&'a T, usize);

impl<T: Display> Display for IndentedDisplay<'_, T> {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let content = format!("{}", self.0);
        for line in content.lines() {
            writeln!(f, "{}{}", "\t".repeat(self.1), line)?;
        }
        Ok(())
    }
}

impl Display for Crate {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        for (i, item) in self.items.iter().enumerate() {
            if i > 0 {
                writeln!(f)?;
            }
            write!(f, "{}", item)?;
        }
        Ok(())
    }
}

impl Display for Item {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Item::TypeAlias(ta) => write!(f, "{}", ta),
            Item::Struct(s) => write!(f, "{}", s),
            Item::Enum(e) => write!(f, "{}", e),
            Item::Trait(t) => write!(f, "{}", t),
            Item::Impl(i) => write!(f, "{}", i),
            Item::Function(func) => write!(f, "{}", func),
            Item::Constant(c) => write!(f, "{}", c),
        }
    }
}

impl Display for FunctionDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "fn {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        write!(f, "(")?;
        for (i, (name, ty)) in self.params.iter().enumerate() {
            if i > 0 {
                write!(f, ", ")?;
            }
            write!(f, "{}: {}", name, ty)?;
        }
        write!(f, ")")?;

        if let Some(ret) = &self.return_type {
            write!(f, " -> {}", ret)?;
        }
        write!(f, "\n")?;

        // Print function spec if present (as attributes for readability)
        if let Some(pre) = &self.spec.pre {
            writeln!(f, "#[pre = {}]", pre)?;
        }
        if let Some(post) = &self.spec.post {
            writeln!(f, "#[post = {}]", post)?;
        }
        writeln!(f, "{{")?;
        write!(f, "{}", IndentedDisplay(&self.body, 1))?;
        write!(f, "}}\n")
    }
}

impl Display for StructDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "struct {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        if self.fields.is_empty() {
            write!(f, ";")?;
        } else {
            writeln!(f, " {{")?;
            for field in &self.fields {
                writeln!(f, "\t{}: {},", field.name, field.ty)?;
            }
            write!(f, "}}")?;
        }
        Ok(())
    }
}

impl Display for EnumDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "enum {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        writeln!(f, " {{")?;
        for variant in &self.variants {
            writeln!(f, "\t{},", variant)?;
        }
        write!(f, "}}")
    }
}

impl Display for VariantDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)?;
        match &self.payload {
            VariantPayload::Unit => Ok(()),
            VariantPayload::Tuple(types) => {
                write!(f, "(")?;
                for (i, ty) in types.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", ty)?;
                }
                write!(f, ")")
            }
            VariantPayload::Struct(fields) => {
                writeln!(f, " {{")?;
                for field in fields {
                    writeln!(f, "\t\t{}: {},", field.name, field.ty)?;
                }
                write!(f, "\t}}")
            }
        }
    }
}

impl Display for TypeAliasDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "type {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        write!(f, " = {};", self.aliased)
    }
}

impl Display for ConstantDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "const {}: {} = {};", self.name, self.ty, self.expr)
    }
}

impl Display for TraitDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "trait {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        if !self.supertraits.is_empty() {
            write!(f, ": ")?;
            for (i, st) in self.supertraits.iter().enumerate() {
                if i > 0 {
                    write!(f, " + ")?;
                }
                write!(f, "{}", st)?;
            }
        }

        writeln!(f, " {{")?;
        for item in &self.items {
            write!(f, "{}", IndentedDisplay(item, 1))?;
        }
        write!(f, "}}")
    }
}

impl Display for TraitItem {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            TraitItem::Method(sig) => write!(f, "{};", sig),
            TraitItem::TypeAlias(ta) => write!(f, "{}", ta),
            TraitItem::Const(c) => write!(f, "{};", c),
        }
    }
}

impl Display for FunctionSig {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "fn {}", self.name)?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        write!(f, "(")?;
        for (i, (name, ty)) in self.params.iter().enumerate() {
            if i > 0 {
                write!(f, ", ")?;
            }
            write!(f, "{}: {}", name, ty)?;
        }
        write!(f, ")")?;

        if let Some(ret) = &self.return_type {
            write!(f, " -> {}", ret)?;
        }

        Ok(())
    }
}

impl Display for ConstantSig {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "const {}: {}", self.name, self.ty)
    }
}

impl Display for ImplDef {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "impl")?;

        if !self.generics.is_empty() {
            write!(f, "<")?;
            for (i, param) in self.generics.iter().enumerate() {
                if i > 0 {
                    write!(f, ", ")?;
                }
                write!(f, "{}", param)?;
            }
            write!(f, ">")?;
        }

        if let Some(trait_ty) = &self.trait_type {
            write!(f, " {} for", trait_ty)?;
        }

        write!(f, " {}", self.self_ty)?;

        writeln!(f, " {{")?;
        for item in &self.items {
            write!(f, "{}", IndentedDisplay(item, 1))?;
            writeln!(f)?;
        }
        write!(f, "}}")
    }
}

impl Display for ImplItem {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            ImplItem::Method(func) => write!(f, "{}", func),
            ImplItem::TypeAlias(ta) => write!(f, "{}", ta),
            ImplItem::Const(c) => write!(f, "{}", c),
        }
    }
}

impl Display for GenericParam {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name)?;
        if !self.bounds.is_empty() {
            write!(f, ": ")?;
            for (i, bound) in self.bounds.iter().enumerate() {
                if i > 0 {
                    write!(f, " + ")?;
                }
                write!(f, "{}", bound)?;
            }
        }
        Ok(())
    }
}

impl Display for Block {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        for stmt in &self.statements {
            writeln!(f, "{}", stmt)?;
        }
        if let Some(tail) = &self.tail {
            write!(f, "{}", tail)?;
        }
        Ok(())
    }
}

impl Display for Stmt {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Stmt::Let { name, ty, init } => {
                write!(f, "let {}", name)?;
                if let Some(ty) = ty {
                    write!(f, ": {}", ty)?;
                }
                if let Some(init) = init {
                    write!(f, " = {}", init)?;
                }
                write!(f, ";")
            }
            Stmt::Expr(expr) => write!(f, "{};", expr),
            Stmt::Assert(expr) => write!(f, "assert!({});", expr),
            Stmt::Return(expr) => {
                write!(f, "return")?;
                if let Some(e) = expr {
                    write!(f, " {}", e)?;
                }
                write!(f, ";")
            }
            Stmt::Break => write!(f, "break;"),
            Stmt::Continue => write!(f, "continue;"),
        }
    }
}

impl Display for Expr {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Expr::Literal(lit) => write!(f, "{}", lit),
            Expr::Path(segments) => write!(f, "{}", segments.join("::")),
            Expr::Call { callee, args } => {
                write!(f, "{}(", callee)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", arg)?;
                }
                write!(f, ")")
            }
            Expr::MethodCall {
                receiver,
                method,
                args,
            } => {
                write!(f, "{}.{}(", receiver, method)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", arg)?;
                }
                write!(f, ")")
            }
            Expr::FieldAccess { target, field } => write!(f, "{}.{}", target, field),
            Expr::Index { target, index } => write!(f, "{}[{}]", target, index),
            Expr::Binary { op, lhs, rhs } => write!(f, "({} {} {})", lhs, op, rhs),
            Expr::Unary { op, expr } => write!(f, "{}{}", op, expr),
            Expr::If {
                cond,
                then_branch,
                else_branch,
            } => {
                write!(f, "if {} {{\n", cond)?;
                write!(f, "{}", IndentedDisplay(then_branch.as_ref(), 1))?;
                write!(f, "}}")?;
                if let Some(else_br) = else_branch {
                    write!(f, " else {{\n")?;
                    write!(f, "{}", IndentedDisplay(else_br.as_ref(), 1))?;
                    write!(f, "}}")?;
                }
                Ok(())
            }
            Expr::While { cond, body } => {
                write!(f, "while {} {{\n", cond)?;
                write!(f, "{}", IndentedDisplay(body.as_ref(), 1))?;
                write!(f, "}}")
            }
            Expr::Match { expr, arms } => {
                write!(f, "match {} {{\n", expr)?;
                for (pattern, block) in arms {
                    write!(f, "\t{} => {{\n", pattern)?;
                    write!(f, "{}", IndentedDisplay(block.as_ref(), 2))?;
                    writeln!(f, "\t}},")?;
                }
                write!(f, "}}")
            }
            Expr::BlockExpr(block) => {
                writeln!(f, "{{")?;
                write!(f, "{}", IndentedDisplay(block.as_ref(), 1))?;
                write!(f, "}}")
            }
            Expr::Cast { expr, ty } => write!(f, "{} as {}", expr, ty),
            Expr::Loop(body) => {
                write!(f, "loop {{\n")?;
                write!(f, "{}", IndentedDisplay(body.as_ref(), 1))?;
                write!(f, "}}")
            }
            Expr::AddressOf { mutable, expr } => {
                if *mutable {
                    write!(f, "&mut {}", expr)
                } else {
                    write!(f, "&{}", expr)
                }
            }
            Expr::TraitUfcsCallExpr(ufcs) => {
                // This uses rust syntax to render the UFCS call.
                // <Self as PathToTrait<TypeArgs...>>::method(args...)
                write!(f, "<")?;
                write!(f, "{} as ", ufcs.self_ty)?;
                write!(f, "{}", ufcs.trait_path.join("::"))?;
                write!(f, "<")?;
                for (i, arg) in ufcs.trait_args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", arg)?;
                }
                write!(f, ">>::{}(", ufcs.method)?;
                for (i, arg) in ufcs.args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", arg)?;
                }
                write!(f, ")")
            }
        }
    }
}

impl Display for Pattern {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Pattern::Wildcard => write!(f, "_"),
            Pattern::Ident(name) => write!(f, "{}", name),
            Pattern::Literal(lit) => write!(f, "{}", lit),
            Pattern::Tuple(patterns) => {
                write!(f, "(")?;
                for (i, pat) in patterns.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", pat)?;
                }
                write!(f, ")")
            }
            Pattern::Struct { path, fields } => {
                write!(f, "{} {{ ", path.join("::"))?;
                for (i, (name, pat)) in fields.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}: {}", name, pat)?;
                }
                write!(f, " }}")
            }
            Pattern::TupleStruct { path, elems } => {
                write!(f, "{}(", path.join("::"))?;
                for (i, elem) in elems.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", elem)?;
                }
                write!(f, ")")
            }
        }
    }
}

impl Display for Literal {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Literal::Int(i) => write!(f, "{}", i),
            Literal::Bool(b) => write!(f, "{}", b),
            Literal::Float(fl) => write!(f, "{}", fl),
            Literal::Char(c) => write!(f, "'{}'", c),
            Literal::String(s) => write!(f, "\"{}\"", s),
            Literal::Unit => write!(f, "()"),
        }
    }
}

impl Display for BinOp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let op = match self {
            BinOp::Add => "+",
            BinOp::Sub => "-",
            BinOp::Mul => "*",
            BinOp::Div => "/",
            BinOp::Rem => "%",
            BinOp::And => "&&",
            BinOp::Or => "||",
            BinOp::Eq => "==",
            BinOp::NotEq => "!=",
            BinOp::Lt => "<",
            BinOp::Le => "<=",
            BinOp::Gt => ">",
            BinOp::Ge => ">=",
            BinOp::Assign => ":=",
        };
        write!(f, "{}", op)
    }
}

impl Display for UnOp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let op = match self {
            UnOp::Neg => "-",
            UnOp::Not => "!",
            UnOp::Deref => "*",
        };
        write!(f, "{}", op)
    }
}

impl Display for Type {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            Type::Never => write!(f, "!"),
            Type::Bool => write!(f, "bool"),
            Type::Char => write!(f, "char"),
            Type::Int(precision) => write!(f, "{}", precision),
            Type::Float(precision) => write!(f, "{}", precision),
            Type::Path(segments) => write!(f, "{}", segments.join("::")),
            Type::Generic { base, args } => {
                write!(f, "{}<", base)?;
                for (i, arg) in args.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", arg)?;
                }
                write!(f, ">")
            }
            Type::TypeParam(name) => write!(f, "{}", name),
            Type::Reference { mutable, inner } => {
                if *mutable {
                    write!(f, "&mut {}", inner)
                } else {
                    write!(f, "&{}", inner)
                }
            }
            Type::Array { inner, len } => {
                if let Some(len) = len {
                    write!(f, "[{}; {}]", inner, len)
                } else {
                    write!(f, "[{}]", inner)
                }
            }
            Type::Tuple(types) => {
                write!(f, "(")?;
                for (i, ty) in types.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", ty)?;
                }
                if types.len() == 1 {
                    write!(f, ",")?; // Single-element tuple needs trailing comma
                }
                write!(f, ")")
            }
            Type::FnPtr { params, ret } => {
                write!(f, "fn(")?;
                for (i, param) in params.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", param)?;
                }
                write!(f, ") -> {}", ret)
            }
            Type::AssocType { type_, name } => write!(f, "<{} as _>::{}", type_, name),
        }
    }
}

impl Display for IntPrecision {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let name = match self {
            IntPrecision::I8 => "i8",
            IntPrecision::I16 => "i16",
            IntPrecision::I32 => "i32",
            IntPrecision::I64 => "i64",
            IntPrecision::I128 => "i128",
            IntPrecision::Isize => "isize",
            IntPrecision::U8 => "u8",
            IntPrecision::U16 => "u16",
            IntPrecision::U32 => "u32",
            IntPrecision::U64 => "u64",
            IntPrecision::U128 => "u128",
            IntPrecision::Usize => "usize",
        };
        write!(f, "{}", name)
    }
}

impl Display for FloatPrecision {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let name = match self {
            FloatPrecision::F32 => "f32",
            FloatPrecision::F64 => "f64",
            FloatPrecision::F128 => "f128",
        };
        write!(f, "{}", name)
    }
}
