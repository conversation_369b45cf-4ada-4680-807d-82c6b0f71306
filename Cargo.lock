# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 4

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "anstream"
version = "0.6.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ae563653d1938f79b1ab1b5e668c87c76a9930414574a6583a7b7e11a8e6192"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "862ed96ca487e809f1c8e5a8447f6ee2cf102f846893800b20cebdf541fc6bbd"

[[package]]
name = "anstyle-parse"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e7644824f0aa2c7b9384579234ef10eb7efb6a0deb83f9630a49594dd9c15c2"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e231f6134f61b71076a3eab506c379d4f36122f2af15a9ff04415ea4c3339e2"
dependencies = [
 "windows-sys 0.60.2",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e0633414522a32ffaac8ac6cc8f748e090c5717661fddeea04219e2344f5f2a"
dependencies = [
 "anstyle",
 "once_cell_polyfill",
 "windows-sys 0.60.2",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"

[[package]]
name = "autocfg"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08606f8c3cbf4ce6ec8e28fb0014a2c086708fe954eaa885384a6165172e7e8"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b8e56985ec62d17e9c1001dc89c88ecd7dc08e47eba5ec7c29c7b5eeecde967"

[[package]]
name = "borsh"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad8646f98db542e39fc66e68a20b2144f6a732636df7c2354e74645faaa433ce"
dependencies = [
 "cfg_aliases",
]

[[package]]
name = "boxcar"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26c4925bc979b677330a8c7fe7a8c94af2dbb4a2d37b4a20a80d884400f46baa"

[[package]]
name = "camino"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0da45bc31171d8d6960122e222a67740df867c1dd53b4d51caa297084c185cab"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.19.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd5eb614ed4c27c5d706420e4320fbe3216ab31fa1c33cd8246ac36dae4479ba"
dependencies = [
 "camino",
 "cargo-platform",
 "semver",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cfg-if"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9555578bc9e57714c812a1f84e4fc5b4d21fcb063490c624de019f7464c91268"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "chalk-derive"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feb14e3ff0ebac26d8e58b6ed1417afb60c4a0a44b6425546ee7eb9c75ebb336"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "chalk-ir"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72f0a61621a088af69fee8df39ec63cf5b6d0b9ab663a740cdeb376aabf2f244"
dependencies = [
 "bitflags 2.9.1",
 "chalk-derive",
]

[[package]]
name = "chalk-recursive"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbd3415cc540015533aa4a8ad007696d585dd9c5f81e7c099872f1dd4bf14894"
dependencies = [
 "chalk-derive",
 "chalk-ir",
 "chalk-solve",
 "rustc-hash 1.1.0",
 "tracing",
]

[[package]]
name = "chalk-solve"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "747707b0c082b3ecf4b1ae28d0d8df708a46cddd22a386f9cc85a312a4de25ff"
dependencies = [
 "chalk-derive",
 "chalk-ir",
 "ena",
 "indexmap",
 "itertools 0.12.1",
 "petgraph",
 "rustc-hash 1.1.0",
 "tracing",
]

[[package]]
name = "clap"
version = "4.5.43"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50fd97c9dc2399518aa331917ac6f274280ec5eb34e555dd291899745c48ec6f"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.43"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c35b5830294e1fa0462034af85cc95225a4cb07092c088c55bda3147cfcd8f65"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
]

[[package]]
name = "clap_derive"
version = "4.5.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef4f52386a59ca4c860f7393bcf8abd8dfd91ecccc0f774635ff68e92eeef491"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "clap_lex"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b94f61472cee1439c0b966b47e3aca9ae07e45d070759512cd390ea2bebc6675"

[[package]]
name = "colorchoice"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b05b61dc5112cbb17e4b6cd61790d9845d13888356391624cbe7e41efeac1e75"

[[package]]
name = "countme"
version = "3.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7704b5fdd17b18ae31c4c1da5a2e0305a2bf17b5249300a9ee9ed7b72114c636"

[[package]]
name = "cov-mark"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "898f9f661ca3c06a58ffe46e34a1e20fdaf5eeba467560f61b96fc85a05fc083"

[[package]]
name = "crossbeam-channel"
version = "0.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b8f8f868b36967f9606790d1903570de9ceaf870a7bf9fbbd3016d636a2cb2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f58bbc28f91df819d0aa2a2c00cd19754769c2fad90579b3592b1c9ba7a3115"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if",
 "crossbeam-utils",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "dot"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a74b6c4d4a1cff5f454164363c16b72fa12463ca6b31f4b5f2035a65fa3d5906"

[[package]]
name = "drop_bomb"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bda8e21c04aca2ae33ffc2fd8c23134f3cac46db123ba97bd9d3f3b8a4a85e1"

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"

[[package]]
name = "ena"
version = "0.14.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d248bdd43ce613d87415282f69b9bb99d947d290b10962dd6c56233312c2ad5"
dependencies = [
 "log",
]

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "fsevent-sys"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76ee7a02da4d231650c7cea31349b889be2f45ddb3ef3032d2ec8185f6313fd2"
dependencies = [
 "libc",
]

[[package]]
name = "fst"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ab85b9b05e3978cc9a9cf8fea7f01b494e1a09ed3037e16ba39edc7a29eb61a"

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"

[[package]]
name = "hashbrown"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5971ac85611da7067dbfcabef3c70ebb5606018acd9e2a3903a0da507521e0d5"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "hashlink"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7382cf6263419f2d8df38c55d7da83da5c18aef87fc7a7fc1fb1e344edfe14c1"
dependencies = [
 "hashbrown 0.15.4",
]

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "home"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589533453244b0995c858700322199b2becb13b627df2851f64a2775d024abcf"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "icu_collections"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "200072f5d0e3614556f94a9930d5dc3e0662a652823904c3a75dc3b0af7fee47"
dependencies = [
 "displaydoc",
 "potential_utf",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locale_core"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cde2700ccaed3872079a65fb1a78f6c0a36c91570f28755dda67bc8f7d9f00a"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_normalizer"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "436880e8e18df4d7bbc06d58432329d6458cc84531f7ac5f024e93deadb37979"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00210d6893afc98edb752b664b8890f0ef174c8adbb8d0be9710fa66fbbf72d3"

[[package]]
name = "icu_properties"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "016c619c1eeb94efb86809b015c58f479963de65bdb6253345c1a1276f22e32b"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locale_core",
 "icu_properties_data",
 "icu_provider",
 "potential_utf",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "298459143998310acd25ffe6810ed544932242d3f07083eee1084d83a71bd632"

[[package]]
name = "icu_provider"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03c80da27b5f4187909049ee2d72f276f0d9f99a42c306bd0131ecfe04d8e5af"
dependencies = [
 "displaydoc",
 "icu_locale_core",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3acae9609540aa318d1bc588455225fb2085b9ed0c4f6bd0d9d5bcd86f1a0344"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "indexmap"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe4cd85333e22411419a0bcae1297d25e58c9443848b11dc6a86fefe8c78a661"
dependencies = [
 "equivalent",
 "hashbrown 0.15.4",
 "serde",
]

[[package]]
name = "inotify"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f37dccff2791ab604f9babef0ba14fbe0be30bd368dc541e2b08d07c8aa908f3"
dependencies = [
 "bitflags 2.9.1",
 "inotify-sys",
 "libc",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5f13b858c8d314ee3e8f639011f7ccefe71f97f96e50151fb991f267928e2c"

[[package]]
name = "jod-thread"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a037eddb7d28de1d0fc42411f501b53b75838d313908078d6698d064f3029b24"

[[package]]
name = "kqueue"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac30106d7dce88daf4a3fcb4879ea939476d5074a9b7ddd0fb97fa4bed5596a"
dependencies = [
 "kqueue-sys",
 "libc",
]

[[package]]
name = "kqueue-sys"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed9625ffda8729b85e45cf04090035ac368927b8cebc34898e7c120f52e4838b"
dependencies = [
 "bitflags 1.3.2",
 "libc",
]

[[package]]
name = "la-arena"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3752f229dcc5a481d60f385fa479ff46818033d881d2d801aa27dffcfb5e8306"

[[package]]
name = "libc"
version = "0.2.174"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1171693293099992e19cddea4e8b849964e9846f4acee11b3948bcc337be8776"

[[package]]
name = "line-index"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e27e0ed5a392a7f5ba0b3808a2afccff16c64933312c84b57618b49d1209bd2"
dependencies = [
 "nohash-hasher",
 "text-size",
]

[[package]]
name = "litemap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "241eaef5fd12c88705a01fc1066c48c4b36e0dd4377dcdc7ec3942cea7a69956"

[[package]]
name = "lock_api"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96936507f153605bddfcda068dd804796c84324ed2510809e5b2a624c81da765"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"

[[package]]
name = "memchr"
version = "2.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a282da65faaf38286cf3be983213fcf1d2e2a58700e808f83f4ea9a4804bc0"

[[package]]
name = "memoffset"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "488016bfae457b036d996092f6cb448677611ce4449e970ceaf42695203f218a"
dependencies = [
 "autocfg",
]

[[package]]
name = "mio"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78bed444cc8a2160f01cbcf811ef18cac863ad68ae8ca62092e8db51d51c761c"
dependencies = [
 "libc",
 "log",
 "wasi",
 "windows-sys 0.59.0",
]

[[package]]
name = "miow"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "359f76430b20a79f9e20e115b3428614e654f04fab314482fc0fda0ebd3c6044"
dependencies = [
 "windows-sys 0.48.0",
]

[[package]]
name = "nohash-hasher"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bf50223579dc7cdcfb3bfcacf7069ff68243f8c363f62ffa99cf000a6b9c451"

[[package]]
name = "notify"
version = "8.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d3d07927151ff8575b7087f245456e549fea62edf0ec4e565a5ee50c8402bc3"
dependencies = [
 "bitflags 2.9.1",
 "fsevent-sys",
 "inotify",
 "kqueue",
 "libc",
 "log",
 "mio",
 "notify-types",
 "walkdir",
 "windows-sys 0.60.2",
]

[[package]]
name = "notify-types"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e0826a989adedc2a244799e823aece04662b66609d96af8dff7ac6df9a8925d"

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"

[[package]]
name = "once_cell_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4895175b425cb1f87721b59f0f286c2092bd4af812243672510e1ac53e2e0ad"

[[package]]
name = "oorandom"
version = "11.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6790f58c7ff633d8771f42965289203411a5e5c68388703c06e14f24770b41e"

[[package]]
name = "parking_lot"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70d58bf43669b5795d1576d0641cfb6fbb2057bf629506267a92807158584a13"
dependencies = [
 "lock_api",
 "parking_lot_core",
]

[[package]]
name = "parking_lot_core"
version = "0.9.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc838d2a56b5b1a6c25f55575dfc605fabb63bb2365f6c2353ef9159aa69e4a5"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "perf-event"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5396562cd2eaa828445d6d34258ae21ee1eb9d40fe626ca7f51c8dccb4af9d66"
dependencies = [
 "libc",
 "perf-event-open-sys",
]

[[package]]
name = "perf-event-open-sys"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce9bedf5da2c234fdf2391ede2b90fabf585355f33100689bc364a3ea558561a"
dependencies = [
 "libc",
]

[[package]]
name = "petgraph"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4c5cc86750666a3ed20bdaf5ca2a0344f9c67674cae0515bec2da16fbaa47db"
dependencies = [
 "fixedbitset",
 "indexmap",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "portable-atomic"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f84267b20a16ea918e43c6a88433c2d54fa145c92a811b5b047ccbe153674483"

[[package]]
name = "potential_utf"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5a7c30837279ca13e7c867e9e40053bc68740f988cb07f7ca6df43cc734b585"
dependencies = [
 "zerovec",
]

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "pulldown-cmark"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57206b407293d2bcd3af849ce869d52068623f19e1b5ff8e8778e3309439682b"
dependencies = [
 "bitflags 2.9.1",
 "memchr",
 "unicase",
]

[[package]]
name = "pulldown-cmark-to-cmark"
version = "10.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0194e6e1966c23cc5fd988714f85b18d548d773e81965413555d96569931833d"
dependencies = [
 "pulldown-cmark",
]

[[package]]
name = "quote"
version = "1.0.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1885c039570dc00dcb4ff087a89e185fd56bae234ddc7f056a945bf36467248d"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "ra-ap-rustc_abi"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "912228bd8ed3beff1f6f9e5e2d4b37c0827ba3e2070060bf3858a311d0e29e30"
dependencies = [
 "bitflags 2.9.1",
 "ra-ap-rustc_hashes",
 "ra-ap-rustc_index",
 "tracing",
]

[[package]]
name = "ra-ap-rustc_hashes"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba520764daf057a9d963fa769f4762eaf87ac5d4900ae76195eeead64cd35afd"
dependencies = [
 "rustc-stable-hash",
]

[[package]]
name = "ra-ap-rustc_index"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b76b5f9ee55f2d0e5a65bea23f6d738893349ce8d3d17a6720933e647ab04978"
dependencies = [
 "ra-ap-rustc_index_macros",
 "smallvec",
]

[[package]]
name = "ra-ap-rustc_index_macros"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddd972eb1face2fcaa0d94c01d97862fb955b5561d4f5932003bce8a6cadd8c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "ra-ap-rustc_lexer"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3a9876456fb2521097deef33ddeac1c18260c8eafb68054d986f8b9d6ce9fa"
dependencies = [
 "memchr",
 "unicode-properties",
 "unicode-xid",
]

[[package]]
name = "ra-ap-rustc_parse_format"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e85de58dfcc60a5f9d5ec0157a657e3f84abd8f22c8a0c4d707cfb42c9011f4"
dependencies = [
 "ra-ap-rustc_lexer",
 "rustc-literal-escaper",
]

[[package]]
name = "ra-ap-rustc_pattern_analysis"
version = "0.110.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ceadf9db550db67deff7eff2e2765109b860c9d7e5bdfca144863020289c823d"
dependencies = [
 "ra-ap-rustc_index",
 "rustc-hash 2.1.1",
 "rustc_apfloat",
 "smallvec",
 "tracing",
]

[[package]]
name = "ra_ap_base_db"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ec4c3d71d49313a76206692c1de6df10277110ae5567f1123d7b14291961bf5"
dependencies = [
 "dashmap",
 "la-arena",
 "ra_ap_cfg",
 "ra_ap_intern",
 "ra_ap_query-group-macro",
 "ra_ap_span",
 "ra_ap_syntax",
 "ra_ap_vfs",
 "rustc-hash 2.1.1",
 "salsa",
 "salsa-macros",
 "semver",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_cfg"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77f6af93a7ee82d51f82841de15a457cdf8106addd70611fbc17f2168beb8a00"
dependencies = [
 "ra_ap_intern",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "tracing",
]

[[package]]
name = "ra_ap_edition"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "045c1117b952421ed95c8c2341f5ea1226211bd86d54cd1a24996df3f252fbf5"

[[package]]
name = "ra_ap_hir"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9db2aac17690c7fbf2fd6f636c19844039bf792aec5a7a0965a7bfb74ea4ff22"
dependencies = [
 "arrayvec",
 "either",
 "indexmap",
 "itertools 0.14.0",
 "ra_ap_base_db",
 "ra_ap_cfg",
 "ra_ap_hir_def",
 "ra_ap_hir_expand",
 "ra_ap_hir_ty",
 "ra_ap_intern",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "smallvec",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_hir_def"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfa8541e93e55b0f08bc21d743f740830454ebd0b868c49bb627df74d301820a"
dependencies = [
 "arrayvec",
 "bitflags 2.9.1",
 "cov-mark",
 "drop_bomb",
 "either",
 "fst",
 "indexmap",
 "itertools 0.14.0",
 "la-arena",
 "ra-ap-rustc_abi",
 "ra-ap-rustc_parse_format",
 "ra_ap_base_db",
 "ra_ap_cfg",
 "ra_ap_hir_expand",
 "ra_ap_intern",
 "ra_ap_mbe",
 "ra_ap_query-group-macro",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "rustc_apfloat",
 "salsa",
 "salsa-macros",
 "smallvec",
 "text-size",
 "thin-vec",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_hir_expand"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08dfc468c1d0ab9fd08b0f3628835912ff9e5e04353ac2985478bbb697afc69a"
dependencies = [
 "cov-mark",
 "either",
 "itertools 0.14.0",
 "ra_ap_base_db",
 "ra_ap_cfg",
 "ra_ap_intern",
 "ra_ap_mbe",
 "ra_ap_parser",
 "ra_ap_query-group-macro",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_syntax-bridge",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "salsa",
 "salsa-macros",
 "smallvec",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_hir_ty"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45053a9c12d0a430371411511f20d4edc8cde7aa8bf99c7c7cfb25d2e0da7674"
dependencies = [
 "arrayvec",
 "bitflags 2.9.1",
 "chalk-derive",
 "chalk-ir",
 "chalk-recursive",
 "chalk-solve",
 "cov-mark",
 "either",
 "ena",
 "indexmap",
 "itertools 0.14.0",
 "la-arena",
 "oorandom",
 "ra-ap-rustc_abi",
 "ra-ap-rustc_index",
 "ra-ap-rustc_pattern_analysis",
 "ra_ap_base_db",
 "ra_ap_hir_def",
 "ra_ap_hir_expand",
 "ra_ap_intern",
 "ra_ap_query-group-macro",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "rustc-hash 2.1.1",
 "rustc_apfloat",
 "salsa",
 "salsa-macros",
 "scoped-tls",
 "smallvec",
 "tracing",
 "triomphe",
 "typed-arena",
]

[[package]]
name = "ra_ap_ide"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9593eaf8e961af71b2281fbe2c1414af16f9944a24ed2c2bf27c4b4903a51a28"
dependencies = [
 "arrayvec",
 "cov-mark",
 "dot",
 "either",
 "itertools 0.14.0",
 "nohash-hasher",
 "oorandom",
 "pulldown-cmark",
 "pulldown-cmark-to-cmark",
 "ra_ap_cfg",
 "ra_ap_hir",
 "ra_ap_ide_assists",
 "ra_ap_ide_completion",
 "ra_ap_ide_db",
 "ra_ap_ide_diagnostics",
 "ra_ap_ide_ssr",
 "ra_ap_profile",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_toolchain",
 "rustc_apfloat",
 "smallvec",
 "tracing",
 "triomphe",
 "url",
]

[[package]]
name = "ra_ap_ide_assists"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12b6415abe2f58097d65260f90ff4c5c31209eb8c68205e4e07e8f6dcf63b777"
dependencies = [
 "cov-mark",
 "either",
 "itertools 0.14.0",
 "ra_ap_hir",
 "ra_ap_ide_db",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "smallvec",
 "tracing",
]

[[package]]
name = "ra_ap_ide_completion"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "224b38f0d486888a055590be6fac22d2a776691d436a9153fc5cc061f40e40fa"
dependencies = [
 "cov-mark",
 "itertools 0.14.0",
 "ra_ap_base_db",
 "ra_ap_hir",
 "ra_ap_ide_db",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "smallvec",
 "tracing",
]

[[package]]
name = "ra_ap_ide_db"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc436c276d6e3a548f9f735613d7e045c66143721eba3af97269ee4e71b9bca5"
dependencies = [
 "arrayvec",
 "bitflags 2.9.1",
 "cov-mark",
 "crossbeam-channel",
 "either",
 "fst",
 "indexmap",
 "itertools 0.14.0",
 "line-index",
 "memchr",
 "nohash-hasher",
 "ra_ap_base_db",
 "ra_ap_hir",
 "ra_ap_parser",
 "ra_ap_profile",
 "ra_ap_query-group-macro",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_vfs",
 "rayon",
 "rustc-hash 2.1.1",
 "salsa",
 "salsa-macros",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_ide_diagnostics"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c7b44e1a7d8130e04701f169162042ac6045f75087cf4ced1cc8448de3b6a3"
dependencies = [
 "cov-mark",
 "either",
 "itertools 0.14.0",
 "ra_ap_cfg",
 "ra_ap_hir",
 "ra_ap_ide_db",
 "ra_ap_paths",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "serde_json",
 "tracing",
]

[[package]]
name = "ra_ap_ide_ssr"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21780edc57b142e0326b6ae94372d4df6b26d0243471c099da90d1d020ee1089"
dependencies = [
 "cov-mark",
 "itertools 0.14.0",
 "ra_ap_hir",
 "ra_ap_ide_db",
 "ra_ap_parser",
 "ra_ap_syntax",
]

[[package]]
name = "ra_ap_intern"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54da8a873c568cf6234e6cfceb3b8a701cc841163e0ea8285f983f65ea76a077"
dependencies = [
 "dashmap",
 "hashbrown 0.14.5",
 "rustc-hash 2.1.1",
 "triomphe",
]

[[package]]
name = "ra_ap_load-cargo"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39b8f0cf8baf96bab0021bc993544b0455ad38a8e9d6e179f3be582e630ceefd"
dependencies = [
 "anyhow",
 "crossbeam-channel",
 "itertools 0.14.0",
 "ra_ap_hir_expand",
 "ra_ap_ide_db",
 "ra_ap_intern",
 "ra_ap_proc_macro_api",
 "ra_ap_project_model",
 "ra_ap_span",
 "ra_ap_tt",
 "ra_ap_vfs",
 "ra_ap_vfs-notify",
 "tracing",
]

[[package]]
name = "ra_ap_mbe"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a420e60b13516b6f82653b3c0ab237b0701e6d197f55b782370595935bb8d6cf"
dependencies = [
 "arrayvec",
 "cov-mark",
 "ra-ap-rustc_lexer",
 "ra_ap_intern",
 "ra_ap_parser",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax-bridge",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "smallvec",
]

[[package]]
name = "ra_ap_parser"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07c08e78d7a66c85b69d24989c0f620f180b4eb51ed1aa370cdc33c3ea4d2b91"
dependencies = [
 "drop_bomb",
 "ra-ap-rustc_lexer",
 "ra_ap_edition",
 "rustc-literal-escaper",
 "tracing",
]

[[package]]
name = "ra_ap_paths"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "982220f7786b13c3827aa1359106ffcde7b7e1b40f39bb4224dbd509379db3c2"
dependencies = [
 "camino",
]

[[package]]
name = "ra_ap_proc_macro_api"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9cf193de89833b871bdfa534da2ca6a75693c49c775912e8d74f4d2016455db"
dependencies = [
 "indexmap",
 "ra_ap_intern",
 "ra_ap_paths",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
 "serde",
 "serde_derive",
 "serde_json",
 "tracing",
]

[[package]]
name = "ra_ap_profile"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46532c1e6c0186e8227135f2838be9d641bcee1164771c8d2c3db04be724f044"
dependencies = [
 "cfg-if",
 "libc",
 "perf-event",
 "windows-sys 0.59.0",
]

[[package]]
name = "ra_ap_project_model"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17bd67bd8f8627eeeb9b572abcef6212aa49e91698875339d8b37796c9527546"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "itertools 0.14.0",
 "la-arena",
 "ra_ap_base_db",
 "ra_ap_cfg",
 "ra_ap_intern",
 "ra_ap_paths",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_toolchain",
 "rustc-hash 2.1.1",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_query-group-macro"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cf72210a4cca32cce1614d5e66327ba9f6fe93df2e91516c6d17dee65a38d44"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "ra_ap_span"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541adc630567635001a11203c8572dcd869c40d5fc42c08aa0d2a3defab99bfc"
dependencies = [
 "hashbrown 0.14.5",
 "la-arena",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_vfs",
 "rustc-hash 2.1.1",
 "salsa",
 "text-size",
]

[[package]]
name = "ra_ap_stdx"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3ac06bbe807ce55d07a7c0b6a70adcf3c5b4d4ed438d35ba8cb36abdb64fdb7"
dependencies = [
 "crossbeam-channel",
 "itertools 0.14.0",
 "jod-thread",
 "libc",
 "miow",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "ra_ap_syntax"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efc79713702a1d3d02b9bee6e095588e306d6cfa846dc4a1cd5478ee4fe47580"
dependencies = [
 "either",
 "itertools 0.14.0",
 "ra_ap_parser",
 "ra_ap_stdx",
 "rowan",
 "rustc-hash 2.1.1",
 "rustc-literal-escaper",
 "smol_str",
 "tracing",
 "triomphe",
]

[[package]]
name = "ra_ap_syntax-bridge"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7531a2162cdc9d87c4fe6eddc8e63e8deed2de0a649bfc1d8061bd4b7e381da"
dependencies = [
 "ra_ap_intern",
 "ra_ap_parser",
 "ra_ap_span",
 "ra_ap_stdx",
 "ra_ap_syntax",
 "ra_ap_tt",
 "rustc-hash 2.1.1",
]

[[package]]
name = "ra_ap_toolchain"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4f053ea1361882f3fe659ec9752ad0df54a40bc0958bb06ae54193838e298fc"
dependencies = [
 "camino",
 "home",
]

[[package]]
name = "ra_ap_tt"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ebefab86d0ef024d094d516e39f57afa9f70b502814e4580b5874a49ea4d44"
dependencies = [
 "arrayvec",
 "ra-ap-rustc_lexer",
 "ra_ap_intern",
 "ra_ap_stdx",
 "text-size",
]

[[package]]
name = "ra_ap_vfs"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c86a1cb84e42249967ab7e90db4c8abf0d68fdbf5a50577f40396f66429bd45b"
dependencies = [
 "crossbeam-channel",
 "fst",
 "indexmap",
 "nohash-hasher",
 "ra_ap_paths",
 "ra_ap_stdx",
 "rustc-hash 2.1.1",
 "tracing",
]

[[package]]
name = "ra_ap_vfs-notify"
version = "0.0.279"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b5843c5243bb543f2474bb5004d1e0d079e2c760d2c78e3af21ea5ab4b28760"
dependencies = [
 "crossbeam-channel",
 "notify",
 "ra_ap_paths",
 "ra_ap_stdx",
 "ra_ap_vfs",
 "rayon",
 "rustc-hash 2.1.1",
 "tracing",
 "walkdir",
]

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "redox_syscall"
version = "0.5.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5407465600fb0548f1442edf71dd20683c6ed326200ace4b1ef0763521bb3b77"
dependencies = [
 "bitflags 2.9.1",
]

[[package]]
name = "rowan"
version = "0.15.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a58fa8a7ccff2aec4f39cc45bf5f985cec7125ab271cf681c279fd00192b49"
dependencies = [
 "countme",
 "hashbrown 0.14.5",
 "memoffset",
 "rustc-hash 1.1.0",
 "text-size",
]

[[package]]
name = "rust_to_lean"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "log",
 "ra_ap_hir",
 "ra_ap_hir_def",
 "ra_ap_hir_ty",
 "ra_ap_ide",
 "ra_ap_ide_db",
 "ra_ap_load-cargo",
 "ra_ap_project_model",
 "ra_ap_span",
 "ra_ap_syntax",
 "ra_ap_vfs",
 "serde",
 "serde_json",
]

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc-literal-escaper"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0041b6238913c41fe704213a4a9329e2f685a156d1781998128b4149c230ad04"

[[package]]
name = "rustc-stable-hash"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "781442f29170c5c93b7185ad559492601acdc71d5bb0706f5868094f45cfcd08"

[[package]]
name = "rustc_apfloat"
version = "0.2.3+llvm-462a31f5a5ab"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "486c2179b4796f65bfe2ee33679acf0927ac83ecf583ad6c91c3b4570911b9ad"
dependencies = [
 "bitflags 2.9.1",
 "smallvec",
]

[[package]]
name = "ryu"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28d3b2b1366ec20994f1fd18c3c594f05c5dd4bc44d8bb0c1c632c8d6829481f"

[[package]]
name = "salsa"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f80d5cf3c3fcab2cef898012f242a670477a1baa609267376af9cb4409026c5"
dependencies = [
 "boxcar",
 "crossbeam-queue",
 "dashmap",
 "hashbrown 0.15.4",
 "hashlink",
 "indexmap",
 "parking_lot",
 "portable-atomic",
 "rayon",
 "rustc-hash 2.1.1",
 "salsa-macro-rules",
 "salsa-macros",
 "smallvec",
 "thin-vec",
 "tracing",
]

[[package]]
name = "salsa-macro-rules"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05303d72606fbf2b9c9523cda2039bb8ecb00304027a3cd7e52b02a65c7d9185"

[[package]]
name = "salsa-macros"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb2f0e2a30c65cb3cd63440c491dde68d9af7e1be2b77832ac7057141107db50"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "scoped-tls"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cf6437eb19a8f4a6cc0f7dca544973b0b78843adbfeb3683d1a94a0024a294"

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "semver"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e6fa9c48d24d85fb3de5ad847117517440f6beceb7798af16b4a87d616b8d0"
dependencies = [
 "serde",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "serde_json"
version = "1.0.142"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "030fedb782600dcbd6f02d479bf0d817ac3bb40d644745b769d6a96bc3afc5a7"
dependencies = [
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "smallvec"
version = "1.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67b1b7a3b5fe4f1376887184045fcf45c69e92af734b7aaddc05fb777b6fbd03"

[[package]]
name = "smol_str"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9676b89cd56310a87b93dec47b11af744f34d5fc9f367b829474eec0a891350d"
dependencies = [
 "borsh",
 "serde",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "syn"
version = "2.0.104"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17b6f705963418cdb9927482fa304bc562ece2fdd4f616084c50b7023b435a40"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "synstructure"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "728a70f3dbaf5bab7f0c4b1ac8d7ae5ea60a4b5549c8a5914361c99147a709d2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "text-size"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f18aa187839b2bdb1ad2fa35ead8c4c2976b64e4363c386d45ac0f7ee85c9233"

[[package]]
name = "thin-vec"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "144f754d318415ac792f9d69fc87abbbfc043ce2ef041c60f16ad828f638717d"

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "tinystr"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d4f6d1145dcb577acf783d4e601bc1d76a13337bb54e6233add580b07344c8b"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81383ab64e72a7a8b8e13130c49e3dab29def6d0c7d76a03087b3cf71c5c6903"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "tracing-core"
version = "0.1.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9d12581f227e93f094d3af2ae690a574abb8a2b9b7a96e7cfe9647b2b617678"
dependencies = [
 "once_cell",
]

[[package]]
name = "triomphe"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef8f7726da4807b58ea5c96fdc122f80702030edc33b35aff9190a51148ccc85"

[[package]]
name = "typed-arena"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6af6ae20167a9ece4bcb41af5b80f8a1f1df981f6391189ce00fd257af04126a"

[[package]]
name = "unicase"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b844d17643ee918803943289730bec8aac480150456169e647ed0b576ba539"

[[package]]
name = "unicode-ident"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a5f39404a5da50712a4c1eecf25e90dd62b613502b7e925fd4e4d19b5c96512"

[[package]]
name = "unicode-properties"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e70f2a8b45122e719eb623c01822704c4e0907e7e426a05927e1a1cfff5b75d0"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
]

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "wasi"
version = "0.11.1+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccf3ec651a847eb01de73ccad15eb7d99f80485de043efb2f370cd654f4ea44b"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "windows-link"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e6ad25900d524eaabdbbb96d20b4311e1e7ae1699af4fb28c17ae66c80d798a"

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.60.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2f500e4d28234f72040990ec9d39e3a6b950f9f22d3dba18416c35882612bcb"
dependencies = [
 "windows-targets 0.53.3",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm 0.52.6",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.53.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5fe6031c4041849d7c496a8ded650796e7b6ecc19df1a431c1a363342e5dc91"
dependencies = [
 "windows-link",
 "windows_aarch64_gnullvm 0.53.0",
 "windows_aarch64_msvc 0.53.0",
 "windows_i686_gnu 0.53.0",
 "windows_i686_gnullvm 0.53.0",
 "windows_i686_msvc 0.53.0",
 "windows_x86_64_gnu 0.53.0",
 "windows_x86_64_gnullvm 0.53.0",
 "windows_x86_64_msvc 0.53.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b8d5f90ddd19cb4a147a5fa63ca848db3df085e25fee3cc10b39b6eebae764"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_aarch64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7651a1f62a11b8cbd5e0d42526e55f2c99886c77e007179efff86c2b137e66c"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1dc67659d35f387f5f6c479dc4e28f1d4bb90ddd1a5d3da2e5d97b42d6272c3"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce6ccbdedbf6d6354471319e781c0dfef054c81fbc7cf83f338a4296c0cae11"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_i686_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581fee95406bb13382d2f65cd4a908ca7b1e4c2f1917f143ba16efe98a589b5d"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e55b5ac9ea33f2fc1716d1742db15574fd6fc8dadc51caab1c16a3d3b4190ba"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a6e035dd0599267ce1ee132e51c27dd29437f63325753051e71dd9e42406c57"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "windows_x86_64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271414315aff87387382ec3d271b52d7ae78726f5d44ac98b4f4030c91880486"

[[package]]
name = "writeable"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea2f10b9bb0928dfb1b42b65e1f9e36f7f54dbdf08457afefb38afcdec4fa2bb"

[[package]]
name = "yoke"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f41bb01b8226ef4bfd589436a297c53d118f65921786300e427be8d487695cc"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38da3c9736e16c5d3c8c597a9aaa5d1fa565d0532ae05e27c24aa62fb32c0ab6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
 "synstructure",
]

[[package]]
name = "zerotrie"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36f0bbd478583f79edad978b407914f61b2972f5af6fa089686016be8f9af595"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
]

[[package]]
name = "zerovec"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7aa2bd55086f1ab526693ecbe444205da57e25f4489879da80635a46d90e73b"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b96237efa0c878c64bd89c436f661be4e46b2f3eff1ebb976f7ef2321d2f58f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]
