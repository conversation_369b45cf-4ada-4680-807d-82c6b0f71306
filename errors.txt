   Compiling rust_to_lean v0.1.0 (/Users/<USER>/Documents/projects/tech/verification-ai/translator-mk-2)
error[E0432]: unresolved imports `crate::logic_core::exhaustive`, `crate::logic_core::matches_pat`
 --> src/wp.rs:5:58
  |
5 | use crate::logic_core::{self as lc, apply_post, cast_ok, exhaustive, in_bounds, matches_pat, neq0, NameGen, Ty};
  |                                                          ^^^^^^^^^^             ^^^^^^^^^^^ no `matches_pat` in `logic_core`
  |                                                          |
  |                                                          no `exhaustive` in `logic_core`

warning: unused import: `self`
 --> src/wp.rs:9:22
  |
9 | use crate::rust_ir::{self, Block, Expr, FunctionDef, Literal, Pattern, Stmt, TraitUfcsCall, Type, BinOp, UnOp};
  |                      ^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

error[E0277]: `rust_ir::Type` doesn't implement `Debug`
   --> src/wp.rs:106:32
    |
106 |         _ => Ty::User(format!("{ir_ty:?}")),
    |                                ^^^^^^^^^ `rust_ir::Type` cannot be formatted using `{:?}`
    |
    = help: the trait `Debug` is not implemented for `rust_ir::Type`
    = note: add `#[derive(Debug)]` to `rust_ir::Type` or manually `impl Debug for rust_ir::Type`
    = note: this error originates in the macro `$crate::__export::format_args` which comes from the expansion of the macro `format` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter: DoubleEndedIterator` is not satisfied
    --> src/wp.rs:197:32
     |
197  |                 es.into_iter().rev().fold(qbody, |acc, e| {
     |                                ^^^ the trait `DoubleEndedIterator` is not implemented for `<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter`
     |
note: required by a bound in `rev`
    --> /Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/iter/traits/iterator.rs:3326:23
     |
3324 |     fn rev(self) -> Rev<Self>
     |        --- required by a bound in this associated function
3325 |     where
3326 |         Self: Sized + DoubleEndedIterator,
     |                       ^^^^^^^^^^^^^^^^^^^ required by this bound in `Iterator::rev`
help: introduce a type parameter with a trait bound instead of using `impl Trait`
     |
196  -             fn eval_chain<'e>(es: impl IntoIterator<Item=&'e Expr>, qbody: lc::Formula, ctx: &mut Ctx, opts: &WpOptions, names: &mut Vec<String>) -> lc::Formula {
196  +             fn eval_chain<'e, I: IntoIterator<Item = &'e Expr>>(es: I, qbody: lc::Formula, ctx: &mut Ctx, opts: &WpOptions, names: &mut Vec<String>) -> lc::Formula where <I as IntoIterator>::IntoIter: DoubleEndedIterator {
     |

error[E0599]: the method `fold` exists for struct `Rev<<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter>`, but its trait bounds were not satisfied
   --> src/wp.rs:197:38
    |
197 |                 es.into_iter().rev().fold(qbody, |acc, e| {
    |                 ---------------------^^^^ method cannot be called due to unsatisfied trait bounds
    |
   ::: /Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/iter/adapters/rev.rs:15:1
    |
15  | pub struct Rev<T> {
    | ----------------- doesn't satisfy `_: Iterator`
    |
    = note: the following trait bounds were not satisfied:
            `<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter: DoubleEndedIterator`
            which is required by `Rev<<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter>: std::iter::Iterator`
            `Rev<<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter>: std::iter::Iterator`
            which is required by `&mut Rev<<impl IntoIterator<Item = &'e Expr> as IntoIterator>::IntoIter>: std::iter::Iterator`

error[E0599]: no variant or associated item named `Ne` found for enum `rust_ir::BinOp` in the current scope
   --> src/wp.rs:356:20
    |
356 |             BinOp::Ne  => lc::Formula::not(lc::Formula::eq(expr_as_term(lhs), expr_as_term(rhs))),
    |                    ^^ variant or associated item not found in `BinOp`
    |
   ::: src/rust_ir.rs:268:1
    |
268 | pub enum BinOp {
    | -------------- variant or associated item `Ne` not found for this enum
    |
help: there is a variant with a similar name
    |
356 -             BinOp::Ne  => lc::Formula::not(lc::Formula::eq(expr_as_term(lhs), expr_as_term(rhs))),
356 +             BinOp::Le  => lc::Formula::not(lc::Formula::eq(expr_as_term(lhs), expr_as_term(rhs))),
    |

warning: unused variable: `params`
  --> src/wp.rs:74:10
   |
74 |     let (params, ret_ty, pre, ens) = boundary_from_ir(f);
   |          ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `ret_ty`
  --> src/wp.rs:74:18
   |
74 |     let (params, ret_ty, pre, ens) = boundary_from_ir(f);
   |                  ^^^^^^ help: if this is intentional, prefix it with an underscore: `_ret_ty`

error[E0502]: cannot borrow `*ctx` as mutable because it is also borrowed as immutable
   --> src/wp.rs:145:34
    |
145 |         Stmt::Return(Some(e)) => wp_expr(e, &ctx.return_post, ctx, opts),
    |                                  -------^^^^----------------^^^^^^^^^^^^
    |                                  |          |
    |                                  |          immutable borrow occurs here
    |                                  mutable borrow occurs here
    |                                  immutable borrow later used by call

warning: unused variable: `all_args`
   --> src/wp.rs:194:21
    |
194 |             let mut all_args: Vec<&Expr> = Vec::with_capacity(args.len() + 1);
    |                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_all_args`

warning: variable does not need to be mutable
   --> src/wp.rs:194:17
    |
194 |             let mut all_args: Vec<&Expr> = Vec::with_capacity(args.len() + 1);
    |                 ----^^^^^^^^
    |                 |
    |                 help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

error[E0004]: non-exhaustive patterns: `&rust_ir::Pattern::Literal(rust_ir::Literal::Float(_))`, `&rust_ir::Pattern::Literal(rust_ir::Literal::Char(_))`, `&rust_ir::Pattern::Literal(rust_ir::Literal::String(_))` and 1 more not covered
   --> src/wp.rs:390:11
    |
390 |     match p {
    |           ^ patterns `&rust_ir::Pattern::Literal(rust_ir::Literal::Float(_))`, `&rust_ir::Pattern::Literal(rust_ir::Literal::Char(_))`, `&rust_ir::Pattern::Literal(rust_ir::Literal::String(_))` and 1 more not covered
    |
note: `rust_ir::Pattern` defined here
   --> src/rust_ir.rs:242:10
    |
242 | pub enum Pattern {
    |          ^^^^^^^
...
245 |     Literal(Literal),
    |     -------
    |     |
    |     not covered
    |     not covered
    |     not covered
    |     not covered
    = note: the matched value is of type `&rust_ir::Pattern`
help: ensure that all possible cases are being handled by adding a match arm with a wildcard pattern as shown, or multiple match arms
    |
401 ~         Pattern::TupleStruct { path, elems } => lc::Term::App("tuple_struct", vec![lc::Term::Var(path.join("::")), lc::Term::Tuple(elems.iter().map(pattern_as_term).collect())]),
402 ~         _ => todo!(),
    |

error[E0004]: non-exhaustive patterns: `&rust_ir::BinOp::Assign` not covered
   --> src/wp.rs:406:11
    |
406 |     match op {
    |           ^^ pattern `&rust_ir::BinOp::Assign` not covered
    |
note: `rust_ir::BinOp` defined here
   --> src/rust_ir.rs:268:10
    |
268 | pub enum BinOp {
    |          ^^^^^
...
282 |     Assign,
    |     ------ not covered
    = note: the matched value is of type `&rust_ir::BinOp`
help: ensure that all possible cases are being handled by adding a match arm with a wildcard pattern or an explicit pattern as shown
    |
419 ~         BinOp::Ge  => ">=",
420 ~         &rust_ir::BinOp::Assign => todo!(),
    |

Some errors have detailed explanations: E0004, E0277, E0432, E0502, E0599.
For more information about an error, try `rustc --explain E0004`.
warning: `rust_to_lean` (bin "rust_to_lean") generated 5 warnings
error: could not compile `rust_to_lean` (bin "rust_to_lean") due to 8 previous errors; 5 warnings emitted
