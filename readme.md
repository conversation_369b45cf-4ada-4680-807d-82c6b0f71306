# Spec

## Todos

* Add support for deferred pre/post conditions.
  - To support other libraries, we should be allowed to assert preconditions
    and postconditions outside of the function definition. Possibly in other
    files, crates, etc.

## Theory and design

This section covers the theory and design of the translator. It informs the
implementation.

### The syntax

Nice—here’s a PL-paper–style abstract syntax for your RustIR. I’m using standard metalanguage:

* `x, f, C, m, X, T` are identifiers (variables, functions, consts, methods, type names, trait names).
* `τ, e, s, p, b, d` range over types, expressions, statements, patterns, blocks, and declarations.
* Lists use `…*` (zero or more), `…+` (one or more), and commas indicate list separators.
* Paths `π` are qualified names (`A::B::C`).
* Loops/for/if-let are already desugared (WP-friendly core).

```text
Programs, Declarations, Generics
--------------------------------
P ∈ Program ::= d*                                     (crate)
d ∈ Decl   ::= type X⟨gp*⟩ = τ                         (type alias)
             | struct X⟨gp*⟩ { (f : τ)* }              (struct)
             | enum   X⟨gp*⟩ { (K vpay)* }            (enum)
             | trait  T⟨gp*⟩ [: σ+] { titem* }         (trait)
             | impl   ⟨gp*⟩ of σ for τ { iitem* }      (trait impl)
             | impl   ⟨gp*⟩ for τ { iitem* }           (inherent impl)
             | fn f⟨gp*⟩( (x : τ)* ) [→ τ] b           (function)
             | const C : τ = e ;                       (constant)

gp ∈ GenParam ::= α [ : σ+ ]                           (type param with bounds)
σ  ∈ Bound    ::= π [⟨τ*⟩]                             (trait/path bound)
π  ∈ Path     ::= X | π :: X                           (qualified name)

Trait / Impl Items
------------------
titem ∈ TraitItem ::= fn f⟨gp*⟩( (x : τ)* ) [→ τ] ;    (method sig)
                    | type X [= τ] ;                   (assoc type sig/def)
                    | const C : τ ;                    (assoc const sig)

iitem ∈ ImplItem  ::= fn f⟨gp*⟩( (x : τ)* ) [→ τ] b    (method def)
                    | type X = τ ;                     (assoc type def)
                    | const C : τ = e ;                (assoc const def)

Types
-----
τ ∈ Ty ::= 1                     (unit)
         | Bool | U8 | U16 | U32 | U64 | U128
         | I8  | I16 | I32 | I64 | I128
         | Usize | Isize | F32 | F64
         | α                                       (type variable)
         | π                                       (named/path type)
         | τ⟨τ*⟩                                   (generic instantiation)
         | & τ | &mut τ                            (references)
         | [τ] | [τ ; n]                           (slice / array)
         | (τ*)                                     (tuple)
         | (τ*,) → τ                                (fn pointer)
         | <τ as π>::X                              (associated type)

Struct/Enum Payloads
--------------------
vpay ∈ VariantPayload ::= •                          (unit)
                         | (τ*)                      (tuple payload)
                         | { (f : τ)* }             (record payload)

Blocks, Statements, Patterns
----------------------------
b ∈ Block ::= { s* [ e ] }                           (optional tail expr)

s ∈ Stmt ::= let x [ : τ ] [ = e ] ;                 (normalized let)
           | assert( e ) ;                           (assert)
           | return [ e ] ;                          (return)
           | break ; | continue ;                    (loop control)
           | e ;                                     (expr as stmt)

p ∈ Pat  ::= _ | x | v                               (wildcard, bind, literal)
           | (p*)                                    (tuple pattern)
           | π { (f : p)* }                          (record pattern)
           | π (p*)                                  (tuple-struct/variant)

Literals
--------
v ∈ Lit ::= () | true | false
          | n            (integer)
          | r            (float)
          | 'c'          (char)
          | "s"          (string)

Expressions
-----------
e ∈ Exp ::= v | x | π                                (lit, var, path)
          | e(e*)                                    (call)
          | e.m(e*)                                  (method call)
          | e.f                                       (field access)
          | e[ e ]                                    (index)
          | e ⊕ e                                     (binary: + − * / % ∧ ∨ == != < ≤ > ≥)
          | ⊖ e                                       (unary: −, ¬)
          | if e b else b                             (conditional)
          | while e b                                 (while loop)
          | match e { (p ⇒ b)* }                      (match)
          | { s* [ e ] }                              (block-as-expr)
          | e as τ                                    (cast)
          | loop b                                    (infinite loop)

Identifiers & Numerics
----------------------
α ∈ TyVar,  x ∈ Var,  f ∈ FnName,  m ∈ MethodName,  C ∈ ConstName,  X ∈ TypeName,  T ∈ TraitName
n ∈ Nat (array length), r ∈ Real (float literal)

Well-Formedness / Normalization Invariants
------------------------------------------
• All `let` are normalized: `let x [: τ] [= e] ;`.
• `for` and `loop { break/continue }` are desugared to `while`/`loop` forms above.
• `if let`, `while let`, and complex matches are desugared into `match`.
• Trait/impl paths in bounds `σ` are fully qualified (`π`) with explicit type arguments.
• Function specs (e.g., requires/ensures) are represented in the IR as `assert`-style nodes
  at entry/exit or attached metadata (omitted from concrete syntax here, but available to WP).
```

If you want, I can add a compact **typing judgment skeleton** (e.g., `Γ ⊢ e : τ`, `Γ ⊢ s ▷ Γ'`) aligned with this syntax to make the WP rules plug-and-play.

### Sketch of the WP rules

Awesome—here’s a WP calculus for your RustIR in the compact “system-of-equations” style typically seen in PL papers. It’s parameterized by a **specification environment** Σ that maps calls to *requires/ensures*, and it distinguishes **statements** vs **expressions** so we can handle block/branch values cleanly.

I’ll write three mutually recursive transformers:

* $\mathrm{wp}_S\llbracket s\rrbracket(Q)$ — statements → predicate
* $\mathrm{wp}_B\llbracket b\rrbracket(Q)$ — blocks (sequence of statements with optional tail expr) → predicate
* $\mathrm{wp}_E\llbracket e\rrbracket(Q(v))$ — expressions (return a value $v$) → predicate

I use $Q, R, \Phi$ for postconditions (predicates over the current state, sometimes also over a value variable), $v$ for expression value variables, and $[t/x]$ for capture-avoiding substitution. Conjunction is $\wedge$, implication $\Rightarrow$.

---

# Specification environment

$$
\Sigma(f) = (\mathrm{Req}_f(\bar{x}),\ \mathrm{Ens}_f(\bar{x}, r))
$$

where $\bar{x}$ are the argument values at the call and $r$ is the return value.

For methods $m$ we assume desugaring to a function symbol in $\Sigma$ (e.g., $m^\sharp$) or a direct entry $\Sigma(m)$.

---

# Statements

**Sequencing**

$$
\mathrm{wp}_S\llbracket s_1; s_2 \rrbracket(Q) \;\;\triangleq\;\; \mathrm{wp}_S\llbracket s_1 \rrbracket\big(\mathrm{wp}_S\llbracket s_2 \rrbracket(Q)\big)
$$

**Let (initialized)**
(our IR normalizes lets; this binds $x$ immutably)

$$
\mathrm{wp}_S\llbracket \texttt{let } x = e; \rrbracket(Q) \;\triangleq\; 
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; Q[v/x]\right)
$$

**Let (havoc / uninitialized)**
(nondeterministic introduction)

$$
\mathrm{wp}_S\llbracket \texttt{let } x; \rrbracket(Q) \;\triangleq\; \forall x.\; Q
$$

**Assert**

$$
\mathrm{wp}_S\llbracket \texttt{assert}(e); \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; v \wedge Q\right)
$$

(Here $e$ is Boolean; typing guarantees $v\in\{\texttt{true},\texttt{false}\}$.)

**Expression statement**

$$
\mathrm{wp}_S\llbracket e; \rrbracket(Q) \;\triangleq\; 
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda\_.\; Q\right)
$$

**Return** (relative to function postcondition $\mathrm{Post}(r)$)
We compute function bodies with a designated return-post $\mathrm{Post}$:

$$
\mathrm{wp}_S^{\mathrm{Post}}\llbracket \texttt{return } e; \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda r.\; \mathrm{Post}(r)\right)
$$

(Observe that $Q$ is ignored—return exits the function.)

**Break/Continue**
For loops, handle via loop-specific posts $\mathrm{BreakPost},\mathrm{ContPost}$ (see “While” below).

---

# Blocks

A block $b \equiv \{ s_1; \dots; s_n;\ [e] \}$ with optional tail $e$:

$$
\begin{aligned}
\mathrm{wp}_B\llbracket \{ s_1;\dots;s_n \} \rrbracket(Q) 
&\triangleq \mathrm{wp}_S\llbracket s_1;\dots;s_n \rrbracket(Q) \\[4pt]
\mathrm{wp}_B\llbracket \{ s_1;\dots;s_n;\ e \} \rrbracket(Q) 
&\triangleq \mathrm{wp}_S\llbracket s_1;\dots;s_n \rrbracket\!\Big(\mathrm{wp}_E\llbracket e \rrbracket(Q)\Big)
\end{aligned}
$$

---

# Expressions

We write $Q(v)$ for a postcondition expecting the value of the expression in meta-variable $v$.

**Literals / Variables / Paths**

$$
\begin{aligned}
\mathrm{wp}_E\llbracket c \rrbracket(Q) &\triangleq Q(c) \\
\mathrm{wp}_E\llbracket x \rrbracket(Q) &\triangleq Q(x) \\
\mathrm{wp}_E\llbracket \pi \rrbracket(Q) &\triangleq Q(\pi) \quad (\text{consts, zero-ary paths})
\end{aligned}
$$

**Binary operators** (pure; left-to-right)

$$
\mathrm{wp}_E\llbracket e_1 \ \oplus\ e_2 \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e_1 \rrbracket\!\left(\lambda v_1.\;
\mathrm{wp}_E\llbracket e_2 \rrbracket\!\left(\lambda v_2.\; \mathrm{safe}_\oplus(v_1,v_2) \wedge Q(v_1 \oplus v_2)\right)\right)
$$

where $\mathrm{safe}_\oplus$ adds definedness side conditions (e.g., $v_2 \neq 0$ for division).

**Unary operators** (pure)

$$
\mathrm{wp}_E\llbracket \ominus e \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; Q(\ominus v)\right)
$$

**Function call** $f(e_1,\dots,e_n)$ with $\Sigma(f)=(\mathrm{Req}_f,\mathrm{Ens}_f)$:
(Left-to-right evaluation)

$$
\begin{aligned}
\mathrm{wp}_E\llbracket f(\bar{e}) \rrbracket(Q) \;\triangleq\;
&\ \mathrm{wp}_E\llbracket e_1 \rrbracket\!\left(\lambda v_1.\; \dots\;
\mathrm{wp}_E\llbracket e_n \rrbracket\!\left(\lambda v_n.\;\right.\right. \\
&\ \ \ \ \left.\left.\mathrm{Req}_f(v_1,\dots,v_n) \ \wedge\ \forall r.\ \mathrm{Ens}_f(v_1,\dots,v_n,r) \Rightarrow Q(r)\right)\dots\right)
\end{aligned}
$$

**Method call** $e_0.m(e_1,\dots,e_k)$: desugar to a function in $\Sigma$ (e.g., $m^\sharp(e_0,\bar{e})$) and apply the rule above.

**Field access / Indexing** (pure)

$$
\begin{aligned}
\mathrm{wp}_E\llbracket e.f \rrbracket(Q) &\triangleq \mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; Q(v.f)\right)\\
\mathrm{wp}_E\llbracket e[e'] \rrbracket(Q) &\triangleq \mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda a.\;
\mathrm{wp}_E\llbracket e' \rrbracket\!\left(\lambda i.\; \mathrm{in\_bounds}(a,i) \wedge Q(a[i])\right)\right)
\end{aligned}
$$

**Cast**

$$
\mathrm{wp}_E\llbracket e \ \texttt{as}\ \tau \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; \mathrm{cast\_ok}(v,\tau) \wedge Q(\mathrm{cast}(v,\tau))\right)
$$

**If-expression** (returns a value)

$$
\mathrm{wp}_E\llbracket \texttt{if } e \ b_1 \ \texttt{else }\ b_2 \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda c.\; (c \Rightarrow \mathrm{wp}_B\llbracket b_1 \rrbracket(Q)) \wedge (\neg c \Rightarrow \mathrm{wp}_B\llbracket b_2 \rrbracket(Q))\right)
$$

**Match-expression** (returns a value)

$$
\mathrm{wp}_E\llbracket \texttt{match } e \ \{\, p_i \Rightarrow b_i \,\}_i \rrbracket(Q) \;\triangleq\;
\mathrm{wp}_E\llbracket e \rrbracket\!\left(\lambda v.\; \bigwedge_i\ \big(\mathrm{matches}(p_i,v) \Rightarrow \mathrm{wp}_B\llbracket b_i \rrbracket(Q)\big)\ \wedge\ \mathrm{exhaustive}(\{p_i\},v)\right)
$$

**Block-expression**

$$
\mathrm{wp}_E\llbracket \{ s^*;\ e \} \rrbracket(Q) \;\triangleq\; \mathrm{wp}_S\llbracket s^* \rrbracket\big(\mathrm{wp}_E\llbracket e \rrbracket(Q)\big)
$$

---

# While loops (with invariant)

For $\texttt{while } e \ b$ we require an invariant $I$ supplied by the user (and optionally a variant $V$ for termination). The **weakest precondition** returned by the loop is $I$, and the following **verification conditions** must be discharged:

$$
\begin{aligned}
\mathrm{wp}_E\llbracket \texttt{while } e \ b \rrbracket(Q) &\;\triangleq\; I \\[4pt]
\textbf{(Init)}\quad & P \Rightarrow I \\
\textbf{(Pres)}\quad & I \wedge e \Rightarrow \mathrm{wp}_B\llbracket b \rrbracket(I) \\
\textbf{(Exit)}\quad & I \wedge \neg e \Rightarrow Q \\
\textbf{(Term)}\quad & I \wedge e \Rightarrow V > 0 \ \ \wedge\ \ \mathrm{wp}_B\llbracket b \rrbracket\big(V' < V\big) \quad \text{(optional, if proving termination)}
\end{aligned}
$$

Here $P$ is the precondition at the loop entry (i.e., the context in which the loop is executed), and $V'$ is the value of the variant after executing $b$.

(If your IR allows `break`/`continue`, introduce branch posts $\mathrm{BreakPost}, \mathrm{ContPost}$ and add $I \wedge e \Rightarrow \mathrm{wp}_B\llbracket b \rrbracket(I,\mathrm{BreakPost},\mathrm{ContPost})$ with side conditions $\mathrm{BreakPost} \Rightarrow Q$ and $\mathrm{ContPost} \Rightarrow I$.)

---

# Functions

Given a function spec $(\mathrm{Pre}_f,\mathrm{Post}_f)$ and body $b$:

$$
\mathrm{VC}(f) \;\triangleq\;
\Big( \mathrm{Pre}_f \Rightarrow \mathrm{wp}_B^{\mathrm{Post}_f}\llbracket b \rrbracket(\mathrm{Post}_f(\texttt{ret})) \Big)
$$

In practice we compute $\mathrm{wp}_B$ with the **return-post** set to $\mathrm{Post}_f$ so that `return e;` produces $\mathrm{Post}_f(e)$ and a tail expression at function end is treated as `return`.

---

# Patterns (for match)

We assume a logical predicate $\mathrm{matches}(p,v)$ defined by equations on the IR’s pattern forms:

* $\mathrm{matches}(\_, v) \triangleq \top$
* $\mathrm{matches}(x, v) \triangleq \top$  (binds $x=v$ in the branch scope)
* $\mathrm{matches}(c, v) \triangleq (v=c)$  (literal)
* $\mathrm{matches}((p_1,\dots,p_k), (v_1,\dots,v_k)) \triangleq \bigwedge_i \mathrm{matches}(p_i,v_i)$
* $\mathrm{matches}(\pi\{f_i:p_i\}, v) \triangleq \mathrm{is\_ctor}(v,\pi) \wedge \bigwedge_i \mathrm{matches}(p_i, v.f_i)$
* $\mathrm{matches}(\pi(p_1,\dots,p_k), v) \triangleq \mathrm{is\_ctor}(v,\pi) \wedge \bigwedge_i \mathrm{matches}(p_i, v.i)$

$\mathrm{exhaustive}(\{p_i\},v)$ asserts the patterns cover all cases of $v$’s type (this becomes a separate VC produced by your pattern checker).

---

# Notes

* The system assumes **pure expressions** except for **calls**, which are accounted for via $\Sigma$. If you later model mutable references or heap updates, add a small-step state model or separation-logic flavored triples for effects; the structure of $\mathrm{wp}$ stays the same but $\Sigma$ becomes Hoare-style specs with frames.
* Division-by-zero, out-of-bounds, invalid casts, etc., are encoded via the $\mathrm{safe}\_\bullet$ side predicates in the corresponding expression rules.
* This calculus supports your goals:

  1. **Normalization of lets** is already in the IR, and the `let` rule substitutes the value.
  2. **Pre/Post/Assert** are expressed directly as antecedents in the corresponding rules; function-level specs are used both at calls and at returns.

If you want, I can turn this into executable VC generation (returning the set of obligations $\mathcal{O}$ alongside $\mathrm{wp}$) or show how to encode these $\mathrm{wp}$ equations as Lean theorems.

## Domain model

* Rust project - A directory containing a rust project. This is the input to the
  translator.
* Verification project - A directory containing a lean4 project. This is the
  output of the translator.
* Translator - A tool that takes a rust project and outputs a verification
  project.
* Rust typed AST - An AST representing the source code of a rust project. It
  contains all type information. Additionally:
  - It contains attributes #[pre = ..], #[post = ..], #![assert = ..] that
    denote preconditions, postconditions, and inline assertions.
* Expanded typed rust AST - An AST representing the expanded source code of a
  rust project. It contains all type information and all macros have been
  expanded. All attributes have been type checked.
* RustIR AST - An intermediate representation of the rust typed AST. It is a
  simplified version of the rust typed AST. It is in a format that is easier to
  reason about and translate to lean4. Specifically, it has been normalized to
  support the computation of WPs.
  - All macros have been expanded.
  - All let expressions have been normalized to `let x [= e]` form.
  - Preconditions, postconditions, and inline assertions are now represented
    directory in the AST instead of appearing as comments.

## CLI

It will be a CLI utility invoked with the following:
* --rust-project-dir: A path to a directory containing a rust project.
* --verification-project-dir: A path to an output directory. This will contain
  an output.
* --up-to-stage: A string denoting which stage we will run the translator up to.
  This is primarily for debugging purposes. By default, the 'complete' stage is
  run.
  * `expand-rust`: Expand all macros in the rust source code.
  * `typed-rust`: Runs up to `expand-rust` and type check the rust AST.
  * `translate`: Runs up to `typed-rust` and translates the expanded AST to
    the RustIR AST.
  * `complete`: Runs the entire pipeline and outputs the RustIR AST to a lean4
    project. This is the default and does not need to be explicitly specified.

## CLI app architecture

This outlines the architecture of the translator application. It is a simple
staged pipeline. The input is a rust project. The output is a lean4 project
containing the rust IR ASTs and proof obligations.

1. Parse and type check the rust project and turn it into a rust_ir AST.
  - Use the `rust-analyzer` APIs to parse and type check the rust project.
  - Macros are expanded.
  - Desugaring is applied:
    - `?` operator is desugared to `match` expressions
    - Assignment arithmetic operators are desugared to their functional
      counterparts. For example, `j += 1` is desugared to `j = j + 1` if `j` is
      an `i32`. Otherwise, it is desugared to the appropriate UFCS call.
    - `for` loops are desugared to `while` loops.
    - `loop` expressions are desugared to `while true` loops.
    - Array operators are desugared to function calls.
    - Indexing is desugared to UFCS calls or, if the array type is primitive, a
      memory offset call.
2. Apply WP algorithm to generate proof obligations.
  - This is done by traversing the RustIR AST and generating proof obligations
    for each function.
3. Output the RustIR AST to a lean4 project.

## Learnings

### Early learnings

* There is an intermediate form of rust called MIR. It is used by the compiler
  for source code processing before converting to LLM. It is SSA-ish and
  monomoprhic.
  * Pros
    * It has some asserts in it.
    * It does some desugaring.
  * Cons
    * Monomorphizes code.
    * Strips pre/post conditions.
    * Lose high level operations.
    * All loops are turned into jumps.
  * Take aways:
    * Not fit for proofs in lean. Lose so much information.
    * But some of the rewrite rules should be considered.

### Learnings from implementing the translation of rust to rust IR

Here’s a concise recap of the new / novel design choices we introduced and refined:

### Learnings about the translation to Lean 4

Regarding integer arithmetic.
- It would be much easier to translate all integer arithmetic to Nat.
- This can be accomplished by translating all int types to Nat and to also translate variables of primitive to propositions that bound the range of the variable. Additionally make sure all operations are safe.
- So inject SafeArith operations and range bounds to WPs.

#### Architecture & Pipeline

* **Hybrid “AST + Semantics” front-end**: Traverse with `ra_ap_syntax` for structure, query `ra_ap_hir::Semantics` for types (`type_of_expr`, `type_of_pat`, `resolve_path`). This mirrors rust-analyzer’s IDE model and gives full inference while keeping traversal simple.
* **Version alignment**: Standardized on `ra_ap_* = 0.0.297` (earlier obstacles with 0.0.279/anymap clarified).
* **Two-source strategy**: Still run `cargo expand` first for high-fidelity macro expansion; then feed expanded sources into the RA-based pipeline for typing and translation.

#### IR Evolution

* **Function specs in IR**: Added `FunctionSpec { pre, post }` and optional `FunctionDef.spec`, so pre/post conditions become first-class, WP-ready data.
* **Richer core language**:

  * Completed support for **structs, enums, type aliases**, with generics and bounds captured.
  * Implemented **pattern matching** (tuple, struct, tuple-struct, literals, wildcard) in the IR.
  * Added **array/slice indexing** (`Expr::Index`), plus later a design to lower it via trait UFCS.
* **Planned UFCS representation**:

  * Proposed `Expr::TraitUfcsCall { trait_path, self_ty, trait_args, method, args }`, `Expr::AddressOf { mutable, … }`, and `UnOp::Deref` to model desugarings precisely (Index/IndexMut, AddAssign, etc.). This makes trait selection explicit for verification.

#### Desugarings & Semantics-Preserved Lowerings

* **Compound assigns**:

  * Interim: `x += e` → `let x = x + e;` (only for simple identifiers).
  * Design for full fidelity: `<Self as core::ops::AddAssign<Rhs>>::add_assign(&mut place, rhs)` using UFCS with explicit `Self`/`Rhs`, enabling correct handling of complex places (`a[i].f += e`) and aliasing.
* **Indexing via traits**:

  * Read: `e[i]` → `<Self as core::ops::Index<Idx>>::index(&e, i)` then `*` (deref).
  * Place/mut: for updates, use `<Self as core::ops::IndexMut<Idx>>::index_mut(&mut e, i)` to obtain `&mut Output`.
    These rely on the UFCS/AddressOf/Deref IR extensions above.

#### Attribute Handling (Pre/Post)

* **Semantics-safety fix**: Attribute expressions are parsed **syntax-only** in a separate mini-parse and **never** passed to `Semantics` (avoids RA panic about cross-database nodes).
* **Block unwrapping**: Normalized `{ <e> }` to `<e>` when printing attributes, preventing `#[pre = { }]` artifacts.

#### Type Conversion Strategy

* **Pragmatic type reconstruction**: Initial mapping from HIR types using display text with lightweight heuristics (paths, generics, primitives). This keeps the translator moving while leaving room to refine references, tuples, fn pointers, and associated types structurally.

#### WP Foundations

* **Formal spec**:

  * Provided **EBNF** for RustIR and a **PLDI/POPL-style abstract syntax** to standardize the core language.
  * Introduced a **WP calculus (system of equations)** with `wp_S`, `wp_B`, `wp_E`, function specs via a global Σ, and loop rules with invariants (and optional variants). This directly informs how IR constructs and desugarings are chosen (e.g., `assert`, `match` blocks, normalized `let`).

#### Developer Experience & Output

* **Pretty-printing**: Kept `fmt::Display` on IR nodes (now including function specs) for readable debug dumps and traceability across stages.
* **Debuggability**: Clear separation of expanded-code artifacts vs. typed/translated IR; explicit failure modes tied to each stage.

#### What this enables next

* Correct trait-aware lowering for **all** op-assigns and indexing in the IR.
* Sound WP generation for programs using **pre/post**, **assert**, **match**, and **loops with invariants**.
* A path to richer type fidelity (references, tuples, associated types) without upheaving the current pipeline.

These choices collectively push the translator toward a spec-driven, verification-friendly IR with explicit semantics (via UFCS and structured specs) while keeping the implementation pragmatic and debuggable.
