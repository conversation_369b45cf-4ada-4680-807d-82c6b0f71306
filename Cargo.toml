[package]
name = "rust_to_lean"
version = "0.1.0"
edition = "2021"

[dependencies]
clap = { version = "4", features = ["derive"] }
anyhow = "1"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
log = "0.4"

ra_ap_ide = "=0.0.279"
ra_ap_ide_db = "=0.0.279"
ra_ap_hir = "=0.0.279"
ra_ap_hir_def = "=0.0.279"
ra_ap_hir_ty = "=0.0.279"
ra_ap_load-cargo = "=0.0.279"
ra_ap_project_model = "=0.0.279"
ra_ap_span = "=0.0.279"
ra_ap_syntax = "=0.0.279"
ra_ap_vfs = "=0.0.279"
